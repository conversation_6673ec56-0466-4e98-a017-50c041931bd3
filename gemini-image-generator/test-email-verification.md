# 🧪 Email Verification Testing Checklist

## Pre-Test Setup

1. **Ensure Firebase is configured:**
   - Check `.env.local` has all Firebase environment variables
   - Verify Firebase project has Email/Password auth enabled
   - Confirm authorized domains include localhost

2. **Start the application:**
   ```bash
   cd gemini-image-generator
   npm run dev
   ```

3. **Open browser to:** `http://localhost:3001`

## Test Cases

### ✅ Test Case 1: Account Creation with Email Verification

**Steps:**
1. Click "Sign In" button in header
2. Click "Don't have an account? Sign up"
3. Fill in form:
   - Display Name: "Test User"
   - Email: Use a real email you can access
   - Password: "testpass123"
4. Click "Create Account"

**Expected Results:**
- ✅ Success toast: "Account created! Please check your email for verification."
- ✅ Modal shows verification message screen
- ✅ Email address is displayed in verification message
- ✅ "I've Verified My Email" and "Resend Verification Email" buttons are visible

### ✅ Test Case 2: Email Delivery

**Steps:**
1. Check your email inbox
2. Look for email from Firebase/your app

**Expected Results:**
- ✅ Verification email received within 1-2 minutes
- ✅ Email contains verification link
- ✅ Email has proper sender information

### ✅ Test Case 3: Unverified Sign-In Attempt

**Steps:**
1. Close verification modal (click X)
2. Try to sign in with the account you just created
3. Use same email/password

**Expected Results:**
- ✅ Error toast: "Please verify your email before signing in..."
- ✅ Modal switches to verification screen
- ✅ Email address is pre-filled

### ✅ Test Case 4: Resend Verification Email

**Steps:**
1. In verification screen, click "Resend Verification Email"
2. Wait for response

**Expected Results:**
- ✅ Success toast: "Verification email sent!"
- ✅ New email received in inbox
- ✅ Button shows loading state during send

### ✅ Test Case 5: Email Verification Process

**Steps:**
1. Open verification email
2. Click the verification link
3. Return to the app
4. Click "I've Verified My Email" button

**Expected Results:**
- ✅ Verification link opens successfully
- ✅ Success toast: "Email verified successfully!"
- ✅ Modal closes
- ✅ User is signed in to the app

### ✅ Test Case 6: Verification Banner (if applicable)

**Steps:**
1. If you can access the app with unverified email, check for banner
2. Test banner functionality

**Expected Results:**
- ✅ Yellow verification banner appears at top
- ✅ Banner shows correct email address
- ✅ "Resend Email" and "I've Verified My Email" buttons work
- ✅ Banner can be dismissed with X button

### ✅ Test Case 7: Google Sign-In (should work without verification)

**Steps:**
1. Sign out if signed in
2. Click "Sign In with Google"
3. Complete Google OAuth flow

**Expected Results:**
- ✅ Google sign-in works normally
- ✅ No verification required for Google accounts
- ✅ User is signed in immediately

## Error Testing

### ❌ Test Case 8: Invalid Email Verification

**Steps:**
1. Try clicking "I've Verified My Email" without actually verifying
2. Test with unverified account

**Expected Results:**
- ✅ Error toast: "Email not verified yet. Please check your email..."
- ✅ Modal remains open
- ✅ User is not signed in

### ❌ Test Case 9: Network Error Handling

**Steps:**
1. Disconnect internet
2. Try to resend verification email
3. Try to check verification status

**Expected Results:**
- ✅ Appropriate error messages shown
- ✅ Loading states handled correctly
- ✅ No app crashes

## Performance Testing

### ⚡ Test Case 10: Loading States

**Steps:**
1. Test all buttons show loading spinners
2. Verify no double-clicks are processed
3. Check button disabled states

**Expected Results:**
- ✅ All async operations show loading states
- ✅ Buttons are disabled during operations
- ✅ No duplicate requests sent

## Browser Compatibility

### 🌐 Test Case 11: Cross-Browser Testing

**Test in:**
- Chrome
- Firefox
- Safari
- Edge

**Expected Results:**
- ✅ All functionality works across browsers
- ✅ UI renders correctly
- ✅ Email verification links work

## Mobile Testing

### 📱 Test Case 12: Mobile Responsiveness

**Steps:**
1. Test on mobile device or browser dev tools
2. Test all verification flows

**Expected Results:**
- ✅ Modal is responsive
- ✅ Buttons are touch-friendly
- ✅ Text is readable
- ✅ Email links work on mobile

## Cleanup

After testing:
1. Delete test accounts from Firebase Console
2. Clear browser storage if needed
3. Document any issues found

## Common Issues & Solutions

**Issue:** Verification emails not received
- Check spam folder
- Verify Firebase SMTP configuration
- Check authorized domains

**Issue:** Verification links don't work
- Ensure authorized domains include your domain
- Check Firebase project configuration
- Verify SSL certificates

**Issue:** "I've Verified My Email" doesn't work
- Ensure user.reload() is called
- Check network connectivity
- Verify Firebase project settings

## Success Criteria

All test cases should pass with:
- ✅ No console errors
- ✅ Proper user feedback
- ✅ Secure verification flow
- ✅ Good user experience
- ✅ Mobile compatibility
