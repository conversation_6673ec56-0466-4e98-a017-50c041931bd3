{"name": "gemini-image-generator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^1.11.0", "@types/uuid": "^10.0.0", "firebase": "^12.0.0", "lucide-react": "^0.533.0", "mongoose": "^8.16.5", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}