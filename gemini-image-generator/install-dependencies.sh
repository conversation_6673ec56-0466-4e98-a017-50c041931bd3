#!/bin/bash

# Gemini AI Flash Image Generator - Dependency Installation Script
# This script resolves common dependency conflicts and installs all required packages

echo "🚀 Installing dependencies for Gemini AI Flash Image Generator..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18.x or 20.x first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  Warning: Node.js version $NODE_VERSION detected. Recommended: 18.x or 20.x"
    echo "Some features may not work correctly with older versions."
fi

echo "✅ Node.js version: $(node -v)"
echo "✅ npm version: $(npm -v)"

# Clean existing installations
echo "🧹 Cleaning existing installations..."
rm -rf node_modules
rm -f package-lock.json

# Clear npm cache
echo "🗑️  Clearing npm cache..."
npm cache clean --force

# Install dependencies with legacy peer deps to resolve conflicts
echo "📦 Installing dependencies..."
npm install --legacy-peer-deps

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
    echo ""
    echo "🎉 Setup complete! You can now run:"
    echo "   npm run dev    # Start development server"
    echo "   npm run build  # Build for production"
    echo ""
    echo "📚 Next steps:"
    echo "1. Copy .env.example to .env.local"
    echo "2. Add your API keys and configuration"
    echo "3. Run 'npm run dev' to start the development server"
    echo ""
    echo "📖 For deployment instructions, see DEPLOYMENT.md"
else
    echo "❌ Installation failed. Please check the error messages above."
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Make sure you have Node.js 18.x or 20.x installed"
    echo "2. Try running: npm install --legacy-peer-deps --force"
    echo "3. Check your internet connection"
    echo "4. If issues persist, delete node_modules and try again"
    exit 1
fi
