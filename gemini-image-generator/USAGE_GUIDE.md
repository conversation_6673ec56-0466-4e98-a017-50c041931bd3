# Gemini AI Flash Image Generator - Usage Guide

## Quick Start

1. **Get Your API Key**
   - Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Sign in with your Google account
   - Click "Create API Key"
   - Copy the generated API key

2. **Start the Application**
   ```bash
   npm run dev
   ```

3. **Open in Browser**
   - Navigate to http://localhost:3000
   - Enter your API key when prompted
   - Start generating images!

## How It Works

### Gemini 2.0 Flash Image Generation

This application uses Google's **Gemini 2.0 Flash Preview Image Generation** model, which can:

- Generate high-quality images from text descriptions
- Support both text and image outputs in the same response
- <PERSON>le complex, detailed prompts
- Generate images in various styles and formats

### Key Features

1. **Text-to-Image**: Describe what you want and get an image
2. **Real-time Generation**: Fast processing with Gemini 2.0 Flash
3. **Image Gallery**: View all your generated images
4. **Download**: Save images to your device
5. **Example Prompts**: Quick-start with pre-made prompts

## Example Prompts

### Realistic Scenes
- "A professional photo of a modern coffee shop interior with warm lighting"
- "An aerial view of a bustling city at golden hour"
- "A close-up portrait of an elderly man with kind eyes, studio lighting"

### Fantasy & Sci-Fi
- "A majestic dragon perched on a crystal mountain peak"
- "A futuristic space station orbiting a purple nebula"
- "A magical forest with glowing mushrooms and fairy lights"

### Art Styles
- "A watercolor painting of a peaceful lake surrounded by autumn trees"
- "A digital art illustration of a cyberpunk street scene"
- "An oil painting in the style of Van Gogh showing a starry night over a village"

### Creative Concepts
- "A robot and a human playing chess in a library"
- "A floating island city in the clouds with waterfalls"
- "A steampunk airship flying through storm clouds"

## Tips for Better Results

### Writing Effective Prompts

1. **Be Specific**: Instead of "a car", try "a red sports car on a mountain road at sunset"
2. **Include Style**: Mention art styles, photography types, or artistic techniques
3. **Add Details**: Include lighting, mood, colors, and composition details
4. **Use Descriptive Words**: Rich adjectives help create better images

### Prompt Structure

A good prompt often follows this pattern:
```
[Subject] + [Setting/Background] + [Style] + [Details]
```

Example:
- **Subject**: "A majestic lion"
- **Setting**: "standing on a rocky cliff"
- **Style**: "in the style of a National Geographic photo"
- **Details**: "with dramatic lighting and a sunset background"

**Full Prompt**: "A majestic lion standing on a rocky cliff in the style of a National Geographic photo with dramatic lighting and a sunset background"

## Troubleshooting

### Common Issues

1. **"Invalid API Key" Error**
   - Double-check your API key is correct
   - Ensure your Google account has access to Gemini API
   - Verify the API key has image generation permissions

2. **"API Quota Exceeded" Error**
   - You've reached your free tier limit
   - Wait for the quota to reset (usually monthly)
   - Consider upgrading to a paid plan

3. **Images Not Generating**
   - Try simpler, more direct prompts
   - Avoid overly complex or contradictory descriptions
   - Check if your prompt violates content policies

4. **Slow Generation**
   - Image generation can take 10-30 seconds
   - Complex prompts may take longer
   - Check your internet connection

### Content Policies

Gemini AI has content policies that restrict:
- Violent or harmful content
- Adult or inappropriate material
- Copyrighted characters or brands
- Misleading or deceptive content

## Technical Details

### API Endpoint
- **Model**: `gemini-2.0-flash-preview-image-generation`
- **Response Modalities**: `["TEXT", "IMAGE"]`
- **Image Format**: PNG (base64 encoded)

### Security
- API keys are handled client-side only
- No persistent storage of keys or images
- Server-side API route for secure communication

### Performance
- Average generation time: 10-30 seconds
- Image resolution: Optimized for web display
- Concurrent requests: Limited by API quotas

## Need Help?

- **API Documentation**: [Gemini API Docs](https://ai.google.dev/gemini-api/docs/image-generation)
- **Google AI Studio**: [aistudio.google.com](https://aistudio.google.com)
- **Community**: [Google AI Forum](https://discuss.ai.google.dev)

## What's Next?

Try experimenting with different:
- Art styles (watercolor, oil painting, digital art)
- Photography types (portrait, landscape, macro)
- Lighting conditions (golden hour, dramatic, soft)
- Moods and atmospheres (peaceful, energetic, mysterious)

Have fun creating amazing images with AI! 🎨✨
