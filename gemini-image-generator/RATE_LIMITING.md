# Daily Rate Limiting System

## 📊 Overview

The Gemini AI Flash Image Generator now includes a comprehensive daily rate limiting system that restricts users to **5 image generations per day**. This helps manage API costs and ensures fair usage across all users.

## 🔧 Implementation Details

### Rate Limiting Rules
- **Daily Limit**: 5 images per user per day
- **Reset Time**: Midnight (00:00) local time
- **Scope**: Per authenticated user (Firebase UID)
- **Enforcement**: Server-side validation before API calls

### Database Schema
```javascript
// RateLimit Model
{
  userId: String,        // Firebase UID
  date: String,          // YYYY-MM-DD format
  count: Number,         // Current usage count
  createdAt: Date,       // Auto-generated
  updatedAt: Date        // Auto-generated
}
```

### Key Features
- **Automatic Cleanup**: Records expire after 30 days
- **Unique Constraints**: One record per user per day
- **Error Handling**: Graceful fallbacks if rate limit service fails
- **Real-time Updates**: UI shows current usage and remaining quota

## 🎯 User Experience

### Rate Limit Display
Users see their current usage status:
```
Daily Usage: 3/5 images
2 remaining
```

### When Limit Reached
- Generate button becomes disabled
- Clear message: "Daily Limit Reached"
- Shows reset time: "Resets at 12:00:00 AM"
- Helpful message: "You've reached your daily limit. Come back tomorrow!"

### Rate Limit Exceeded Response
```json
{
  "error": "Daily limit of 5 images reached. You have generated 5 images today. Limit resets at midnight.",
  "rateLimitExceeded": true,
  "resetTime": "2024-01-02T00:00:00.000Z",
  "current": 5,
  "limit": 5
}
```

## 🔌 API Endpoints

### Check Rate Limit Status
```
GET /api/rate-limit/[userId]
```

**Response:**
```json
{
  "success": true,
  "allowed": true,
  "remaining": 2,
  "current": 3,
  "limit": 5,
  "resetTime": "2024-01-02T00:00:00.000Z"
}
```

### Generate Image (with Rate Limiting)
```
POST /api/generate-image
```

**Rate Limited Response (429):**
```json
{
  "error": "Daily limit of 5 images reached...",
  "rateLimitExceeded": true,
  "resetTime": "2024-01-02T00:00:00.000Z",
  "current": 5,
  "limit": 5
}
```

## 🛠️ Technical Implementation

### Rate Limit Check Flow
1. User initiates image generation
2. Server checks current usage for today
3. If under limit: increment counter and proceed
4. If at limit: return 429 error with details
5. UI updates to show current status

### Database Operations
```javascript
// Check and increment rate limit
const rateLimitResult = await checkAndIncrementRateLimit(userId);

if (!rateLimitResult.allowed) {
  return NextResponse.json({
    error: "Daily limit reached",
    rateLimitExceeded: true,
    // ... other details
  }, { status: 429 });
}
```

### Client-Side Integration
```javascript
// UI shows rate limit info
{user && rateLimitInfo && (
  <div className="rate-limit-display">
    Daily Usage: {rateLimitInfo.current}/{rateLimitInfo.limit} images
    {rateLimitInfo.remaining > 0 ? 
      `${rateLimitInfo.remaining} remaining` : 
      `Resets at ${rateLimitInfo.resetTime.toLocaleTimeString()}`
    }
  </div>
)}

// Button disabled when limit reached
<button 
  disabled={rateLimitInfo?.remaining === 0}
  onClick={generateImage}
>
  {rateLimitInfo?.remaining === 0 ? 
    'Daily Limit Reached' : 
    'Generate Image'
  }
</button>
```

## 📈 Admin Features

### Rate Limit Statistics
```javascript
// Get overall usage stats
const stats = await getRateLimitStats();
// Returns: { totalUsers, usersAtLimit, totalGenerationsToday }
```

### Reset User Limit (Admin)
```javascript
// Reset a specific user's daily limit
const success = await resetUserRateLimit(userId);
```

## 🔒 Security Features

### Server-Side Enforcement
- All rate limiting happens on the server
- Client-side UI is for UX only
- Cannot be bypassed by API calls
- Atomic database operations prevent race conditions

### Data Protection
- Rate limit data automatically expires
- No sensitive information stored
- Indexed for fast lookups
- Handles concurrent requests safely

## 🎛️ Configuration

### Adjusting the Daily Limit
To change the daily limit, update the constant in `/src/lib/rateLimit.ts`:

```javascript
export const DAILY_LIMIT = 5; // Change this value
```

### Time Zone Considerations
- Rate limits reset at midnight server time
- Consider user time zones for better UX
- Current implementation uses UTC

## 📊 Monitoring & Analytics

### Usage Tracking
- Track daily generation counts
- Monitor users hitting limits
- Analyze usage patterns
- Identify potential abuse

### Metrics Available
- Total active users per day
- Users at rate limit
- Total generations per day
- Peak usage times

## 🚀 Deployment Notes

### Environment Variables
No additional environment variables needed. Rate limiting uses the existing MongoDB connection.

### Database Indexes
Automatically created indexes:
- `{ userId: 1, date: 1 }` (unique)
- `{ createdAt: 1 }` (TTL - 30 days)

### Performance Impact
- Minimal overhead (single DB query per generation)
- Efficient indexes for fast lookups
- Automatic cleanup prevents data bloat

## 🔄 Future Enhancements

### Potential Improvements
1. **Tiered Limits**: Different limits for different user types
2. **Time-based Limits**: Hourly limits in addition to daily
3. **Premium Users**: Higher limits for paid accounts
4. **Usage Analytics**: Detailed usage dashboards
5. **Flexible Reset Times**: User-configurable reset times

### Scaling Considerations
- Current implementation scales to millions of users
- Consider Redis for high-traffic scenarios
- Monitor database performance as usage grows

## ✅ Testing

### Test Scenarios
1. **Normal Usage**: Generate images within limit
2. **Limit Reached**: Try to generate when at limit
3. **Reset Functionality**: Verify daily reset works
4. **Concurrent Requests**: Multiple simultaneous generations
5. **Error Handling**: Database failures and edge cases

### Manual Testing
1. Generate 5 images in a day
2. Verify 6th generation is blocked
3. Check UI shows correct status
4. Verify reset at midnight

The rate limiting system is now fully operational and ready for production deployment! 🎉
