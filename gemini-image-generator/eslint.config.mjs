import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Allow unused variables that start with underscore
      "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
      // Allow img elements (we'll optimize later)
      "@next/next/no-img-element": "warn",
      // Allow missing dependencies in useEffect (we handle them carefully)
      "react-hooks/exhaustive-deps": "warn",
      // Allow missing alt text (we'll add them gradually)
      "jsx-a11y/alt-text": "warn",
    },
  },
];

export default eslintConfig;
