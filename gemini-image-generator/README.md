# Gemini AI Flash Image Generator - Social Media Platform

A full-featured social media platform for AI image generation using Google's Gemini 2.0 Flash AI model. Create, share, and discover amazing AI-generated images with a vibrant community.

## 🌟 Features

### Core Generation
- **Text-to-Image Generation**: Create images using natural language descriptions
- **Style & Mood Controls**: Choose from artistic styles and emotional moods
- **Real-time Generation**: Fast image generation using Gemini 2.0 Flash
- **Smart Prompts**: Example prompts and suggestions to get started

### Social Features
- **User Authentication**: Firebase Auth with Google sign-in and email/password
- **User Profiles**: Personalized profiles with avatars and bios
- **Public Gallery**: Share your creations with the community
- **Like & Comment System**: Engage with other users' creations
- **Follow System**: Follow your favorite creators

### Gallery & Organization
- **Personal Gallery**: View and manage all your generated images
- **Collections**: Organize images into themed collections
- **Search & Filter**: Find images by style, mood, or keywords
- **Favorites System**: Save your favorite creations
- **Bulk Download**: Download multiple images at once

### Advanced Features
- **Random Prompt Generator**: Get creative inspiration
- **Style Explorer**: Browse different art movements and styles
- **Color Palette Selector**: Generate images with specific color schemes
- **Mood Board Creator**: Combine multiple images into collections
- **Progressive Web App**: Install as mobile app

## 🚀 Prerequisites

- Node.js 18+ (recommended: Node.js 20+)
- A Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
- A MongoDB database (MongoDB Atlas recommended)
- A Firebase project for authentication

## 🛠️ Setup Instructions

### 1. Clone and Install
```bash
<NAME_EMAIL>:oni1997/gemini-image-generator.git
cd gemini-image-generator
```

**Install dependencies** (choose one option):

**Option A: Use the installation script (Recommended)**:
```bash
# On macOS/Linux:
./install-dependencies.sh

# On Windows:
install-dependencies.bat
```

**Option B: Manual installation**:
```bash
npm install --legacy-peer-deps
```

> **Note**: If you encounter dependency conflicts (ERESOLVE errors), the installation script will automatically resolve them.

### 2. Get Your API Keys

#### Gemini API Key
- Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
- Create a new API key
- Copy the API key

#### MongoDB Setup
- Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
- Create a new cluster
- Get your connection string

#### Firebase Setup
- Go to [Firebase Console](https://console.firebase.google.com)
- Create a new project
- Enable Authentication with Google and Email/Password
- Get your Firebase configuration

### 3. Environment Variables
Create a `.env.local` file in the root directory:

```env
# Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/gemini-image-generator

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef123456
```

### 4. Run the Application
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) and start creating!

## How to Use

1. **Enter API Key**: When you first open the app, you'll be prompted to enter your Gemini API key
2. **Write a Prompt**: Describe the image you want to generate (e.g., "A futuristic city with flying cars at sunset")
3. **Generate**: Click "Generate Image" or press Enter
4. **View Results**: Your generated image will appear in the gallery below
5. **Download**: Click the download button to save images to your device

## Example Prompts

- "A majestic dragon flying over a medieval castle at sunset"
- "A cyberpunk street scene with neon lights and rain"
- "A peaceful zen garden with cherry blossoms"
- "A space station orbiting a distant planet"
- "A cozy coffee shop on a rainy day"

## Technical Details

### Built With

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Gemini 2.0 Flash**: Google's latest multimodal AI model

### API Integration

The app uses Google's Gemini 2.0 Flash Preview Image Generation model:
- Model: `gemini-2.0-flash-preview-image-generation`
- Response modalities: `["TEXT", "IMAGE"]`
- Supports both text and image outputs

## 🚨 Troubleshooting

### Common Issues

#### Dependency Conflicts (ERESOLVE errors)
If you encounter dependency resolution errors:
```bash
# Clean and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
```

#### Node.js Version Issues
- **Recommended**: Node.js 18.x or 20.x
- **Minimum**: Node.js 16.x
- Use `nvm` to manage Node.js versions if needed

#### Firebase Authentication Issues
- Ensure all Firebase environment variables are set correctly
- Check that your domain is added to Firebase authorized domains
- Verify Firebase project is active and billing is enabled if required

#### MongoDB Connection Issues
- Check your MongoDB connection string format
- Ensure IP whitelist includes your deployment IP (or use 0.0.0.0/0)
- Verify database user has proper permissions

#### Gemini API Issues
- Verify your API key is valid and active
- Check API quotas and billing in Google AI Studio
- Ensure the Gemini API is enabled for your project

## 🚀 Deployment

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

**Quick Deploy to Vercel**:
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy!

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.
