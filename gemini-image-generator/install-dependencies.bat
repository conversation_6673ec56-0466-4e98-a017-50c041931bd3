@echo off
REM Gemini AI Flash Image Generator - Dependency Installation Script (Windows)
REM This script resolves common dependency conflicts and installs all required packages

echo 🚀 Installing dependencies for Gemini AI Flash Image Generator...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18.x or 20.x first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js version:
node --version
echo ✅ npm version:
npm --version

REM Clean existing installations
echo 🧹 Cleaning existing installations...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

REM Clear npm cache
echo 🗑️  Clearing npm cache...
npm cache clean --force

REM Install dependencies with legacy peer deps to resolve conflicts
echo 📦 Installing dependencies...
npm install --legacy-peer-deps

if %errorlevel% equ 0 (
    echo ✅ Dependencies installed successfully!
    echo.
    echo 🎉 Setup complete! You can now run:
    echo    npm run dev    # Start development server
    echo    npm run build  # Build for production
    echo.
    echo 📚 Next steps:
    echo 1. Copy .env.example to .env.local
    echo 2. Add your API keys and configuration
    echo 3. Run 'npm run dev' to start the development server
    echo.
    echo 📖 For deployment instructions, see DEPLOYMENT.md
) else (
    echo ❌ Installation failed. Please check the error messages above.
    echo.
    echo 🔧 Troubleshooting:
    echo 1. Make sure you have Node.js 18.x or 20.x installed
    echo 2. Try running: npm install --legacy-peer-deps --force
    echo 3. Check your internet connection
    echo 4. If issues persist, delete node_modules and try again
)

pause
