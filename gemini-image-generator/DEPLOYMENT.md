# Deployment Guide - Gemini AI Flash Image Generator

This guide will help you deploy your Gemini AI Flash Image Generator to Vercel with all the necessary environment variables and configurations.

## 🚀 Vercel Deployment

### Step 1: Prepare Your Repository

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Initial commit - Gemini AI Flash Image Generator"
   git push origin main
   ```

### Step 2: Deploy to Vercel

1. **Connect to Vercel**:
   - Go to [Vercel](https://vercel.com)
   - Sign in with your GitHub account
   - Click "New Project"
   - Import your repository

2. **Configure Build Settings**:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install --legacy-peer-deps`
   - Node.js Version: 18.x or 20.x

### Step 3: Environment Variables

In your Vercel project settings, add these environment variables:

#### Required Environment Variables

```env
# Gemini API Key (REQUIRED)
GEMINI_API_KEY=your_gemini_api_key_here

# MongoDB Connection (REQUIRED)
MONGODB_URI=mongodb+srv://username:<EMAIL>/gemini-image-generator?retryWrites=true&w=majority

# Firebase Configuration (REQUIRED)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef123456
```

### Step 4: Domain Configuration

1. **Custom Domain** (Optional):
   - Go to your Vercel project settings
   - Add your custom domain
   - Update Firebase Auth domain settings

2. **Update Firebase Configuration**:
   - Go to Firebase Console > Authentication > Settings
   - Add your Vercel domain to authorized domains
   - Update redirect URLs if using OAuth

## 🔧 Environment Setup Details

### 1. Gemini API Key

**Where to get it**:
- Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
- Sign in with your Google account
- Click "Create API Key"
- Copy the generated key

**Important Notes**:
- Keep this key secure and never expose it in client-side code
- Monitor your usage in Google AI Studio
- Set up billing alerts if needed

### 2. MongoDB Atlas Setup

**Create Database**:
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free cluster
3. Create a database user
4. Whitelist your IP addresses (or use 0.0.0.0/0 for all IPs)
5. Get your connection string

**Connection String Format**:
```
mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
```

**Database Collections**:
The app will automatically create these collections:
- `users` - User profiles and settings
- `images` - Generated images and metadata
- `collections` - User-created image collections
- `comments` - Comments on images

### 3. Firebase Authentication Setup

**Create Firebase Project**:
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Authentication
4. Set up sign-in methods:
   - Email/Password
   - Google (recommended)

**Get Configuration**:
1. Go to Project Settings > General
2. Scroll down to "Your apps"
3. Click the web app icon
4. Copy the configuration values

**Security Rules**:
Make sure to configure proper security rules for your Firebase project.

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env.local` to version control
- Use Vercel's environment variable interface
- Separate development and production environments

### API Keys
- Regularly rotate your API keys
- Monitor usage and set up alerts
- Use least-privilege access principles

### Database Security
- Use strong passwords for MongoDB
- Enable IP whitelisting
- Regular security updates

## 📊 Monitoring and Analytics

### Vercel Analytics
- Enable Vercel Analytics in your project settings
- Monitor performance and usage

### Error Tracking
Consider adding error tracking services like:
- Sentry
- LogRocket
- Bugsnag

### Performance Monitoring
- Use Vercel's built-in performance monitoring
- Monitor API response times
- Track image generation success rates

## 🚨 Troubleshooting

### Common Deployment Issues

1. **Dependency Resolution Errors**:
   ```bash
   # If you encounter ERESOLVE errors, use:
   npm install --legacy-peer-deps

   # Or clear cache and reinstall:
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install --legacy-peer-deps
   ```

2. **Build Failures**:
   - Check Node.js version compatibility (use 18.x or 20.x)
   - Verify all dependencies are installed
   - Check for TypeScript errors
   - Use `--legacy-peer-deps` flag if needed

3. **Environment Variable Issues**:
   - Ensure all required variables are set
   - Check for typos in variable names
   - Verify Firebase configuration
   - Make sure NEXT_PUBLIC_ prefix is used for client-side variables

4. **Database Connection Issues**:
   - Check MongoDB connection string
   - Verify IP whitelisting (use 0.0.0.0/0 for all IPs if needed)
   - Test connection locally first
   - Ensure MongoDB Atlas cluster is running

5. **Authentication Issues**:
   - Verify Firebase configuration
   - Check authorized domains in Firebase Console
   - Test authentication locally
   - Ensure Firebase project is active

### Getting Help

- Check Vercel deployment logs
- Review Firebase console for auth issues
- Monitor MongoDB Atlas for connection issues
- Check browser console for client-side errors

## 🎯 Post-Deployment Checklist

- [ ] Test image generation functionality
- [ ] Verify user authentication works
- [ ] Check database connections
- [ ] Test on mobile devices
- [ ] Verify all environment variables are set
- [ ] Monitor initial usage and performance
- [ ] Set up error tracking
- [ ] Configure domain and SSL
- [ ] Update Firebase authorized domains
- [ ] Test all social features (likes, comments, etc.)

## 📈 Scaling Considerations

As your app grows, consider:

1. **Database Optimization**:
   - Add proper indexes
   - Implement caching
   - Consider database sharding

2. **Image Storage**:
   - Move to cloud storage (AWS S3, Google Cloud Storage)
   - Implement CDN for faster image delivery
   - Add image compression

3. **API Rate Limiting**:
   - Implement rate limiting
   - Add user quotas
   - Monitor API usage

4. **Performance**:
   - Add Redis for caching
   - Implement image lazy loading
   - Optimize bundle size

Your Gemini AI Flash Image Generator is now ready for production! 🎉
