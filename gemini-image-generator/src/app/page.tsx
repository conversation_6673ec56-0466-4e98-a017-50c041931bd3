'use client';

import ImageGenerator from '@/components/ImageGenerator';

export default function Home() {
  return (
    <main className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-800 dark:text-white mb-4">
          AI Art Studio
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-4 max-w-2xl mx-auto">
          Create stunning visuals and share with a vibrant creative community
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-8">
          Powered by Google&apos;s cutting-edge Gemini 2.0 Flash AI
        </p>

        {/* Feature highlights */}
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <span className="text-2xl">🎨</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Professional Quality</span>
          </div>
          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <span className="text-2xl">⚡</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Lightning Fast</span>
          </div>
          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <span className="text-2xl">🌍</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Creative Community</span>
          </div>
          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <span className="text-2xl">🚀</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Latest AI Technology</span>
          </div>
        </div>
      </div>

      <ImageGenerator />

      {/* Community Showcase Section */}
      <div className="mt-16 text-center">
        <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
          Join Our Creative Community
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          Discover amazing AI art from creators worldwide, share your masterpieces, and get inspired by the community.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="text-4xl mb-4">🎨</div>
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">Create & Share</h3>
            <p className="text-gray-600 dark:text-gray-300">Generate professional-quality AI art and share with the community</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="text-4xl mb-4">💬</div>
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">Engage & Connect</h3>
            <p className="text-gray-600 dark:text-gray-300">Like, comment, and connect with fellow artists and creators</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="text-4xl mb-4">🚀</div>
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">Learn & Grow</h3>
            <p className="text-gray-600 dark:text-gray-300">Export generation scripts and learn from the community&apos;s techniques</p>
          </div>
        </div>

        <div className="flex flex-wrap justify-center gap-4">
          <a
            href="/explore"
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Explore Community Art
          </a>
          <a
            href="/gallery"
            className="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            View Your Gallery
          </a>
        </div>
      </div>
    </main>
  );
}
