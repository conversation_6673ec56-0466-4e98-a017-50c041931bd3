'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Check, Star, Zap, Crown, Building } from 'lucide-react';
import toast from 'react-hot-toast';

interface PricingTier {
  id: string;
  name: string;
  price: number;
  interval: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonStyle: string;
}

export default function PricingPage() {
  const { user } = useAuth();
  const [currentTier, setCurrentTier] = useState<string>('free');
  const [loading, setLoading] = useState(false);

  const pricingTiers: PricingTier[] = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      interval: 'forever',
      description: 'Perfect for getting started with AI art',
      icon: <Star className="w-6 h-6" />,
      features: [
        '5 images per day',
        'Standard quality',
        'Community access',
        'Basic styles & moods',
        'Public gallery',
        'Download images',
      ],
      buttonText: 'Current Plan',
      buttonStyle: 'bg-gray-200 text-gray-600 cursor-not-allowed',
    },
    {
      id: 'creator',
      name: 'Creator',
      price: 9.99,
      interval: 'month',
      description: 'For serious AI artists and content creators',
      icon: <Zap className="w-6 h-6" />,
      features: [
        '50 images per day',
        'HD quality generation',
        'No watermarks',
        'Priority generation queue',
        'Custom style presets',
        'Batch generation (up to 5)',
        'Image upscaling',
        'Advanced editing tools',
      ],
      popular: true,
      buttonText: 'Upgrade to Creator',
      buttonStyle: 'bg-indigo-600 hover:bg-indigo-700 text-white',
    },
    {
      id: 'pro',
      name: 'Pro',
      price: 29.99,
      interval: 'month',
      description: 'For professionals and businesses',
      icon: <Crown className="w-6 h-6" />,
      features: [
        '200 images per day',
        'Ultra HD quality',
        'Commercial license',
        'API access',
        'Batch generation (up to 20)',
        'Custom model training',
        'Priority support',
        'Advanced analytics',
        'Team collaboration',
      ],
      buttonText: 'Upgrade to Pro',
      buttonStyle: 'bg-purple-600 hover:bg-purple-700 text-white',
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 99.99,
      interval: 'month',
      description: 'For large teams and organizations',
      icon: <Building className="w-6 h-6" />,
      features: [
        'Unlimited images',
        'White-label solution',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantee',
        'On-premise deployment',
        'Custom model training',
        'Advanced security',
        'Team management',
      ],
      buttonText: 'Contact Sales',
      buttonStyle: 'bg-gray-800 hover:bg-gray-900 text-white',
    },
  ];

  useEffect(() => {
    if (user) {
      loadCurrentSubscription();
    }
  }, [user]);

  const loadCurrentSubscription = async () => {
    try {
      const response = await fetch(`/api/subscription?userId=${user?.uid}`);
      const data = await response.json();
      
      if (data.success) {
        setCurrentTier(data.subscription.tier);
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
    }
  };

  const handleUpgrade = async (tierId: string) => {
    if (!user) {
      toast.error('Please sign in to upgrade');
      return;
    }

    if (tierId === 'enterprise') {
      // Open contact form or redirect to sales
      window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
      return;
    }

    if (tierId === currentTier) {
      return;
    }

    setLoading(true);
    try {
      // In a real implementation, this would integrate with Stripe
      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.uid,
          tier: tierId,
          // Mock Stripe data - in real implementation, this would come from Stripe
          stripeData: {
            customerId: 'cus_mock',
            subscriptionId: 'sub_mock',
            priceId: `price_${tierId}`,
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            amount: pricingTiers.find(t => t.id === tierId)?.price || 0,
            currency: 'usd',
            interval: 'month',
          },
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setCurrentTier(tierId);
        toast.success(data.message);
      } else {
        toast.error(data.error || 'Failed to upgrade');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast.error('Failed to upgrade subscription');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
          Choose Your Plan
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Unlock the full potential of AI art creation with our flexible pricing plans
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
        {pricingTiers.map((tier) => (
          <div
            key={tier.id}
            className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 transition-all hover:shadow-lg ${
              tier.popular 
                ? 'border-indigo-500 scale-105' 
                : 'border-gray-200 dark:border-gray-700'
            } ${
              currentTier === tier.id 
                ? 'ring-2 ring-green-500' 
                : ''
            }`}
          >
            {/* Popular Badge */}
            {tier.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
            )}

            {/* Current Plan Badge */}
            {currentTier === tier.id && (
              <div className="absolute -top-3 right-4">
                <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Current
                </span>
              </div>
            )}

            <div className="p-6">
              {/* Header */}
              <div className="text-center mb-6">
                <div className="flex items-center justify-center mb-3">
                  <div className={`p-3 rounded-full ${
                    tier.popular ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tier.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                  {tier.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                  {tier.description}
                </p>
                <div className="mb-4">
                  <span className="text-3xl font-bold text-gray-800 dark:text-white">
                    ${tier.price}
                  </span>
                  <span className="text-gray-600 dark:text-gray-300">
                    /{tier.interval}
                  </span>
                </div>
              </div>

              {/* Features */}
              <ul className="space-y-3 mb-6">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              {/* Button */}
              <button
                onClick={() => handleUpgrade(tier.id)}
                disabled={loading || currentTier === tier.id}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  currentTier === tier.id 
                    ? 'bg-green-100 text-green-800 cursor-not-allowed'
                    : tier.buttonStyle
                }`}
              >
                {loading ? 'Processing...' : 
                 currentTier === tier.id ? 'Current Plan' : 
                 tier.buttonText}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* FAQ Section */}
      <div className="mt-16 max-w-3xl mx-auto">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white text-center mb-8">
          Frequently Asked Questions
        </h2>
        
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 className="font-semibold text-gray-800 dark:text-white mb-2">
              Can I change my plan anytime?
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we&apos;ll prorate any billing differences.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 className="font-semibold text-gray-800 dark:text-white mb-2">
              What happens to my images if I downgrade?
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              All your existing images remain accessible. However, you&apos;ll be limited to the features and daily limits of your new plan going forward.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 className="font-semibold text-gray-800 dark:text-white mb-2">
              Do you offer refunds?
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              We offer a 30-day money-back guarantee for all paid plans. If you&apos;re not satisfied, contact our support team for a full refund.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      {!user && (
        <div className="mt-16 text-center bg-indigo-50 dark:bg-indigo-900/20 p-8 rounded-lg">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
            Ready to Get Started?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Sign up now and start creating amazing AI art today!
          </p>
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
            Sign Up Free
          </button>
        </div>
      )}
    </div>
  );
}
