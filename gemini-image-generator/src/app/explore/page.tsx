'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Heart, Download, Share2, User, Compass, Filter, MessageCircle, Info } from 'lucide-react';
import toast from 'react-hot-toast';
import CommentSection from '@/components/CommentSection';
import ImageDetailsModal from '@/components/ImageDetailsModal';

interface PublicImage {
  _id: string;
  imageUrl: string;
  prompt: string;
  style?: string;
  mood?: string;
  likes: string[];
  generationTime: number;
  createdAt: string;
  user: {
    displayName: string;
    photoURL?: string;
    username: string;
  };
}

export default function ExplorePage() {
  const { user } = useAuth();
  const [images, setImages] = useState<PublicImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [styleFilter, setStyleFilter] = useState('');
  const [moodFilter, setMoodFilter] = useState('');
  const [showComments, setShowComments] = useState<string | null>(null);
  const [showImageDetails, setShowImageDetails] = useState<PublicImage | null>(null);

  const loadPublicImages = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (styleFilter) params.append('style', styleFilter);
      if (moodFilter) params.append('mood', moodFilter);
      
      const response = await fetch(`/api/images/public?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setImages(data.images);
      }
    } catch (error) {
      console.error('Error loading public images:', error);
      toast.error('Failed to load images');
    } finally {
      setLoading(false);
    }
  }, [styleFilter, moodFilter]);

  useEffect(() => {
    loadPublicImages();
  }, [loadPublicImages]);

  const likeImage = async (imageId: string) => {
    if (!user) {
      toast.error('Please sign in to like images');
      return;
    }

    // Prevent multiple rapid clicks
    const currentImage = images.find(img => img._id === imageId);
    if (!currentImage) return;

    try {
      const response = await fetch(`/api/images/${imageId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.uid }),
      });

      if (response.ok) {
        const data = await response.json();
        setImages(prev =>
          prev.map(img =>
            img._id === imageId
              ? {
                  ...img,
                  likes: data.isLiked
                    ? [...img.likes.filter(id => id !== user.uid), user.uid] // Ensure uniqueness
                    : img.likes.filter(id => id !== user.uid)
                }
              : img
          )
        );
        toast.success(data.isLiked ? 'Image liked!' : 'Image unliked!');
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to like image');
      }
    } catch (error) {
      console.error('Error liking image:', error);
      toast.error('Failed to like image');
    }
  };

  const downloadImage = (imageUrl: string, prompt: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `gemini-generated-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const shareImage = async (imageUrl: string, prompt: string, imageId: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Generated Image',
          text: `Check out this AI-generated image: "${prompt}"`,
          url: window.location.origin + '/image/' + imageId,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      const shareUrl = window.location.origin + '/image/' + imageId;
      navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard!');
    }
  };

  const styles = ['realistic', 'artistic', 'cartoon', 'abstract', 'vintage', 'modern', 'fantasy', 'sci-fi'];
  const moods = ['happy', 'sad', 'energetic', 'calm', 'mysterious', 'dramatic', 'peaceful', 'intense'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <Compass className="w-8 h-8 text-indigo-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
              Explore
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Discover amazing AI-generated images from the community
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Filter className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Filters</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Style
              </label>
              <select
                value={styleFilter}
                onChange={(e) => setStyleFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Styles</option>
                {styles.map(style => (
                  <option key={style} value={style}>
                    {style.charAt(0).toUpperCase() + style.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mood
              </label>
              <select
                value={moodFilter}
                onChange={(e) => setMoodFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Moods</option>
                {moods.map(mood => (
                  <option key={mood} value={mood}>
                    {mood.charAt(0).toUpperCase() + mood.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Images Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-300 mt-4">Loading community images...</p>
          </div>
        ) : images.length === 0 ? (
          <div className="text-center py-12">
            <Compass className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              No public images found
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Be the first to share your creations with the community!
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {images.map((image) => (
              <div key={image._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden group">
                <div className="aspect-square relative">
                  <img
                    src={image.imageUrl}
                    alt={image.prompt}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-2">
                      <button
                        onClick={() => likeImage(image._id)}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all ${
                          user && image.likes.includes(user.uid) ? 'text-red-500' : 'text-gray-700'
                        }`}
                        title="Like"
                      >
                        <Heart className={`w-5 h-5 ${user && image.likes.includes(user.uid) ? 'fill-current' : ''}`} />
                      </button>
                      <button
                        onClick={() => setShowComments(image._id)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Comments"
                      >
                        <MessageCircle className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => setShowImageDetails(image)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="View Details & Script"
                      >
                        <Info className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => downloadImage(image.imageUrl, image.prompt)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Download"
                      >
                        <Download className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => shareImage(image.imageUrl, image.prompt, image._id)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Share"
                      >
                        <Share2 className="w-5 h-5 text-gray-700" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="p-4">
                  {/* User info */}
                  <div className="flex items-center gap-2 mb-3">
                    {image.user.photoURL ? (
                      <img
                        src={image.user.photoURL}
                        alt={image.user.displayName}
                        className="w-6 h-6 rounded-full"
                      />
                    ) : (
                      <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
                        <User className="w-3 h-3 text-white" />
                      </div>
                    )}
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {image.user.displayName}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                    {image.prompt}
                  </p>
                  
                  {(image.style || image.mood) && (
                    <div className="flex gap-2 mb-2">
                      {image.style && (
                        <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 text-xs rounded-full">
                          {image.style}
                        </span>
                      )}
                      {image.mood && (
                        <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full">
                          {image.mood}
                        </span>
                      )}
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                    <span>{new Date(image.createdAt).toLocaleDateString()}</span>
                    <div className="flex items-center gap-2">
                      <Heart className="w-3 h-3" />
                      <span>{image.likes.length}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Comment Modal */}
        {showComments && (
          <CommentSection
            imageId={showComments}
            isOpen={!!showComments}
            onClose={() => setShowComments(null)}
          />
        )}

        {/* Image Details Modal */}
        {showImageDetails && (
          <ImageDetailsModal
            image={showImageDetails}
            isOpen={!!showImageDetails}
            onClose={() => setShowImageDetails(null)}
            onLike={likeImage}
            currentUserId={user?.uid}
          />
        )}
      </div>
    </div>
  );
}
