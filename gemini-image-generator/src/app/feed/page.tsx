'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Heart, MessageCircle, Share2, Download, Info, Users, Sparkles } from 'lucide-react';
import toast from 'react-hot-toast';

interface FeedImage {
  _id: string;
  userId: string;
  prompt: string;
  imageUrl: string;
  style?: string;
  mood?: string;
  isPublic: boolean;
  likes: string[];
  comments: any[];
  createdAt: string;
  isFromFollowedUser: boolean;
  isOwnImage: boolean;
  generationTime?: number;
}

export default function FeedPage() {
  const { user } = useAuth();
  const [images, setImages] = useState<FeedImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (user) {
      loadFeed();
    }
  }, [user]);

  const loadFeed = async (pageNum = 1) => {
    if (!user) return;

    try {
      setLoading(pageNum === 1);

      const response = await fetch(
        `/api/feed?userId=${user.uid}&page=${pageNum}&limit=20`
      );
      const data = await response.json();

      if (data.success) {
        if (pageNum === 1) {
          setImages(data.images);
        } else {
          setImages(prev => [...prev, ...data.images]);
        }
        setHasMore(data.pagination.page < data.pagination.pages);
        setPage(pageNum);
      } else {
        toast.error('Failed to load feed');
      }
    } catch (error) {
      console.error('Error loading feed:', error);
      toast.error('Failed to load feed');
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      loadFeed(page + 1);
    }
  };

  const likeImage = async (imageId: string) => {
    if (!user) {
      toast.error('Please sign in to like images');
      return;
    }

    try {
      const response = await fetch(`/api/images/${imageId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.uid }),
      });

      const data = await response.json();
      if (data.success) {
        setImages(images.map(img => 
          img._id === imageId 
            ? { 
                ...img, 
                likes: data.liked 
                  ? [...img.likes, user.uid]
                  : img.likes.filter(id => id !== user.uid)
              }
            : img
        ));
      }
    } catch (error) {
      console.error('Error liking image:', error);
      toast.error('Failed to like image');
    }
  };

  const shareImage = async (imageUrl: string, prompt: string, imageId: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Generated Image',
          text: `Check out this AI-generated image: "${prompt}"`,
          url: window.location.origin + '/image/' + imageId,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      const shareUrl = window.location.origin + '/image/' + imageId;
      navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard!');
    }
  };

  const downloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `ai-art-${prompt.slice(0, 30).replace(/[^a-z0-9]/gi, '-')}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Image downloaded!');
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download image');
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-600 dark:text-gray-300 mb-2">
            Sign In to See Your Feed
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Follow other creators to see their latest AI art in your personalized feed
          </p>
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
            Sign In
          </button>
        </div>
      </div>
    );
  }

  if (loading && images.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2 flex items-center justify-center gap-2">
          <Sparkles className="w-8 h-8 text-indigo-600" />
          Your Feed
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Latest AI art from creators you follow
        </p>
      </div>

      {/* Feed */}
      {images.length === 0 ? (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">
            Your feed is empty
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Follow other creators to see their latest AI art here
          </p>
          <div className="flex gap-4 justify-center">
            <a
              href="/explore"
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Explore Community
            </a>
            <a
              href="/"
              className="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Create Your First Image
            </a>
          </div>
        </div>
      ) : (
        <div className="max-w-2xl mx-auto space-y-8">
          {images.map((image) => (
            <div
              key={image._id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              {/* Image Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {image.userId.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-800 dark:text-white">
                        {image.isOwnImage ? 'You' : 'Creator'}
                      </span>
                      {image.isFromFollowedUser && (
                        <span className="ml-2 text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                          Following
                        </span>
                      )}
                    </div>
                  </div>
                  <span className="text-sm text-gray-500">
                    {new Date(image.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Image */}
              <div className="relative">
                <img
                  src={image.imageUrl}
                  alt={image.prompt}
                  className="w-full h-auto"
                />
              </div>

              {/* Image Actions */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => likeImage(image._id)}
                      className={`flex items-center gap-2 transition-colors ${
                        user && image.likes.includes(user.uid)
                          ? 'text-red-600'
                          : 'text-gray-600 dark:text-gray-300 hover:text-red-600'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${
                        user && image.likes.includes(user.uid) ? 'fill-current' : ''
                      }`} />
                      <span>{image.likes.length}</span>
                    </button>

                    <button className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 transition-colors">
                      <MessageCircle className="w-5 h-5" />
                      <span>{image.comments.length}</span>
                    </button>

                    <button
                      onClick={() => shareImage(image.imageUrl, image.prompt, image._id)}
                      className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 transition-colors"
                    >
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => downloadImage(image.imageUrl, image.prompt)}
                      className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 transition-colors"
                    >
                      <Download className="w-5 h-5" />
                    </button>
                    <button className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 transition-colors">
                      <Info className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Prompt */}
                <p className="text-gray-800 dark:text-white mb-2">
                  <span className="font-medium">Prompt:</span> {image.prompt}
                </p>

                {/* Style and Mood */}
                {(image.style || image.mood) && (
                  <div className="flex gap-2 mb-2">
                    {image.style && (
                      <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                        {image.style}
                      </span>
                    )}
                    {image.mood && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {image.mood}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Load More */}
          {hasMore && (
            <div className="text-center py-8">
              <button
                onClick={loadMore}
                disabled={loading}
                className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
