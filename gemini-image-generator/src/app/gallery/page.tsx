'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Folder, Image as ImageIcon, Heart, Download, Share2, Eye, EyeOff, MessageCircle, Info } from 'lucide-react';
import toast from 'react-hot-toast';
import CommentSection from '@/components/CommentSection';
import ImageDetailsModal from '@/components/ImageDetailsModal';

interface UserImage {
  _id: string;
  imageUrl: string;
  prompt: string;
  style?: string;
  mood?: string;
  isPublic: boolean;
  likes?: string[];
  generationTime: number;
  createdAt: string;
}

export default function GalleryPage() {
  const { user } = useAuth();
  const [images, setImages] = useState<UserImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'public' | 'private'>('all');
  const [showComments, setShowComments] = useState<string | null>(null);
  const [showImageDetails, setShowImageDetails] = useState<UserImage | null>(null);

  useEffect(() => {
    if (user) {
      loadUserImages();
    }
  }, [user]);

  const loadUserImages = async () => {
    if (!user) return;
    
    try {
      const response = await fetch(`/api/images/user?userId=${user.uid}`);
      if (response.ok) {
        const data = await response.json();
        setImages(data);
      }
    } catch (error) {
      console.error('Error loading images:', error);
      toast.error('Failed to load images');
    } finally {
      setLoading(false);
    }
  };

  const toggleImageVisibility = async (imageId: string, currentPublic: boolean) => {
    try {
      const response = await fetch(`/api/images/${imageId}/visibility`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPublic: !currentPublic,
          userId: user?.uid,
        }),
      });

      if (response.ok) {
        setImages(prev => 
          prev.map(img => 
            img._id === imageId 
              ? { ...img, isPublic: !currentPublic }
              : img
          )
        );
        toast.success(`Image made ${!currentPublic ? 'public' : 'private'}`);
      }
    } catch (error) {
      console.error('Error updating visibility:', error);
      toast.error('Failed to update visibility');
    }
  };

  const downloadImage = (imageUrl: string, prompt: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `gemini-generated-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const likeImage = async (imageId: string) => {
    if (!user) {
      toast.error('Please sign in to like images');
      return;
    }

    try {
      const response = await fetch(`/api/images/${imageId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.uid }),
      });

      if (response.ok) {
        const data = await response.json();
        setImages(prev =>
          prev.map(img =>
            img._id === imageId
              ? {
                  ...img,
                  likes: data.isLiked
                    ? [...(img.likes || []).filter(id => id !== user.uid), user.uid]
                    : (img.likes || []).filter(id => id !== user.uid)
                }
              : img
          )
        );
        toast.success(data.isLiked ? 'Image liked!' : 'Image unliked!');
      }
    } catch (error) {
      console.error('Error liking image:', error);
      toast.error('Failed to like image');
    }
  };

  const shareImage = async (imageUrl: string, prompt: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Generated Image',
          text: `Check out this AI-generated image: "${prompt}"`,
          url: window.location.origin + '/image/' + images.find(img => img.imageUrl === imageUrl)?._id,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      const shareUrl = window.location.origin + '/image/' + images.find(img => img.imageUrl === imageUrl)?._id;
      navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard!');
    }
  };

  const filteredImages = images.filter(img => {
    if (filter === 'public') return img.isPublic;
    if (filter === 'private') return !img.isPublic;
    return true;
  });

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Sign In Required
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Please sign in to view your gallery
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <Folder className="w-8 h-8 text-indigo-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
              My Gallery
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {images.length} images created
            </p>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex gap-4 mb-8">
          {[
            { key: 'all', label: 'All Images', count: images.length },
            { key: 'public', label: 'Public', count: images.filter(img => img.isPublic).length },
            { key: 'private', label: 'Private', count: images.filter(img => !img.isPublic).length },
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as 'all' | 'public' | 'private')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                filter === tab.key
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {/* Images Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-300 mt-4">Loading your images...</p>
          </div>
        ) : filteredImages.length === 0 ? (
          <div className="text-center py-12">
            <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              No images found
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {filter === 'all' 
                ? 'Start creating some amazing AI images!'
                : `No ${filter} images yet.`
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredImages.map((image) => (
              <div key={image._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden group">
                <div className="aspect-square relative">
                  <img
                    src={image.imageUrl}
                    alt={image.prompt}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-2">
                      <button
                        onClick={() => likeImage(image._id)}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all ${
                          user && image.likes?.includes(user.uid) ? 'text-red-500' : 'text-gray-700'
                        }`}
                        title="Like"
                      >
                        <Heart className={`w-5 h-5 ${user && image.likes?.includes(user.uid) ? 'fill-current' : ''}`} />
                      </button>
                      <button
                        onClick={() => setShowComments(image._id)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Comments"
                      >
                        <MessageCircle className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => setShowImageDetails(image)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="View Details & Script"
                      >
                        <Info className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => toggleImageVisibility(image._id, image.isPublic)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title={image.isPublic ? 'Make Private' : 'Make Public'}
                      >
                        {image.isPublic ? (
                          <Eye className="w-5 h-5 text-green-600" />
                        ) : (
                          <EyeOff className="w-5 h-5 text-gray-600" />
                        )}
                      </button>
                      <button
                        onClick={() => downloadImage(image.imageUrl, image.prompt)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Download"
                      >
                        <Download className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => shareImage(image.imageUrl, image.prompt)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Share"
                      >
                        <Share2 className="w-5 h-5 text-gray-700" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Visibility indicator */}
                  <div className="absolute top-2 right-2">
                    {image.isPublic ? (
                      <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        Public
                      </div>
                    ) : (
                      <div className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        Private
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                    {image.prompt}
                  </p>
                  
                  {(image.style || image.mood) && (
                    <div className="flex gap-2 mb-2">
                      {image.style && (
                        <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 text-xs rounded-full">
                          {image.style}
                        </span>
                      )}
                      {image.mood && (
                        <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full">
                          {image.mood}
                        </span>
                      )}
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                    <span>{new Date(image.createdAt).toLocaleDateString()}</span>
                    <div className="flex items-center gap-2">
                      <Heart className="w-3 h-3" />
                      <span>{image.likes?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Comment Modal */}
        {showComments && (
          <CommentSection
            imageId={showComments}
            isOpen={!!showComments}
            onClose={() => setShowComments(null)}
          />
        )}

        {/* Image Details Modal */}
        {showImageDetails && (
          <ImageDetailsModal
            image={showImageDetails}
            isOpen={!!showImageDetails}
            onClose={() => setShowImageDetails(null)}
            onLike={likeImage}
            currentUserId={user?.uid}
          />
        )}
      </div>
    </div>
  );
}
