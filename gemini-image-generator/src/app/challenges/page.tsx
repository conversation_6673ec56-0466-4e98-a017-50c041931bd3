'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ChallengeCard from '@/components/ChallengeCard';
import { Trophy, Calendar, Users, Filter } from 'lucide-react';
import toast from 'react-hot-toast';

interface Challenge {
  _id: string;
  title: string;
  description: string;
  theme: string;
  hashtag: string;
  type: string;
  category: string;
  difficulty: string;
  startDate: string;
  endDate: string;
  votingEndDate: string;
  status: string;
  submissionCount: number;
  participantCount: number;
  prizes: string[];
  daysRemaining: number;
  votingDaysRemaining: number;
  isFeatured: boolean;
}

export default function ChallengesPage() {
  const { user } = useAuth();
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState('active');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'All Challenges' },
    { value: 'active', label: 'Active' },
    { value: 'voting', label: 'Voting Phase' },
    { value: 'upcoming', label: 'Upcoming' },
    { value: 'completed', label: 'Completed' },
  ];

  useEffect(() => {
    loadChallenges();
  }, [selectedStatus, showFeaturedOnly]);

  const loadChallenges = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        status: selectedStatus,
        featured: showFeaturedOnly.toString(),
        limit: '20',
      });

      const response = await fetch(`/api/challenges?${params}`);
      const data = await response.json();

      if (data.success) {
        setChallenges(data.challenges || []);
      } else {
        toast.error('Failed to load challenges');
      }
    } catch (error) {
      console.error('Error loading challenges:', error);
      toast.error('Failed to load challenges');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinChallenge = (challengeId: string) => {
    // Navigate to challenge detail page or open submission modal
    window.location.href = `/challenges/${challengeId}`;
  };

  const getStatusStats = () => {
    const stats = challenges.reduce((acc, challenge) => {
      acc[challenge.status] = (acc[challenge.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      active: stats.active || 0,
      voting: stats.voting || 0,
      upcoming: stats.upcoming || 0,
      completed: stats.completed || 0,
    };
  };

  const stats = getStatusStats();

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
          Community Challenges
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Join creative challenges, showcase your AI art skills, and compete with the community for amazing prizes!
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Trophy className="w-5 h-5 text-green-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {stats.active}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Active</span>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Users className="w-5 h-5 text-yellow-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {stats.voting}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Voting</span>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Calendar className="w-5 h-5 text-blue-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {stats.upcoming}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Upcoming</span>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Trophy className="w-5 h-5 text-gray-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {stats.completed}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Completed</span>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center mb-8">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>

        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={showFeaturedOnly}
            onChange={(e) => setShowFeaturedOnly(e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">Featured only</span>
        </label>
      </div>

      {/* Challenges Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {challenges.map((challenge) => (
          <ChallengeCard
            key={challenge._id}
            challenge={challenge}
            onJoinChallenge={handleJoinChallenge}
          />
        ))}
      </div>

      {challenges.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">
            No challenges found
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {selectedStatus === 'active' 
              ? 'No active challenges at the moment. Check back soon!'
              : 'Try adjusting your filters to see more challenges.'
            }
          </p>
        </div>
      )}

      {/* Call to Action */}
      {!user && (
        <div className="mt-12 text-center bg-indigo-50 dark:bg-indigo-900/20 p-8 rounded-lg">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
            Ready to Join the Competition?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Sign in to participate in challenges and showcase your AI art skills!
          </p>
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
            Sign In to Participate
          </button>
        </div>
      )}
    </div>
  );
}
