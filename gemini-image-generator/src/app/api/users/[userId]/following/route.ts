import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Follow from '@/models/Follow';
import User from '@/models/User';

// GET /api/users/[userId]/following - Get users that this user follows
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const { userId } = await params;

    // Get following
    const following = await Follow.find({ followerId: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get following user details
    const followingIds = following.map(f => f.followingId);
    const followingUsers = await User.find({ 
      firebaseUid: { $in: followingIds } 
    }).lean();

    // Combine follow data with user data
    const followingWithDetails = following.map(follow => {
      const user = followingUsers.find(u => u.firebaseUid === follow.followingId);
      return {
        ...follow,
        user: user ? {
          firebaseUid: user.firebaseUid,
          displayName: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          bio: user.bio,
        } : null,
      };
    });

    const total = await Follow.countDocuments({ followerId: userId });

    return NextResponse.json({
      success: true,
      following: followingWithDetails,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching following:', error);
    return NextResponse.json(
      { error: 'Failed to fetch following' },
      { status: 500 }
    );
  }
}
