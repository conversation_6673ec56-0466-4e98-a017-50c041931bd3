import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomStyle from '@/models/CustomStyle';

// GET /api/users/[userId]/styles - Get styles created by user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const requestingUserId = searchParams.get('requestingUserId');
    const includePrivate = searchParams.get('includePrivate') === 'true';
    const { userId } = await params;

    // Build query
    const query: any = { createdBy: userId };

    // If not the owner, only show public approved styles
    if (requestingUserId !== userId || !includePrivate) {
      query.isPublic = true;
      query.isApproved = true;
    }

    const styles = await CustomStyle.find(query)
      .sort({ createdAt: -1 })
      .lean();

    return NextResponse.json({
      success: true,
      styles,
    });
  } catch (error) {
    console.error('Error fetching user styles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user styles' },
      { status: 500 }
    );
  }
}
