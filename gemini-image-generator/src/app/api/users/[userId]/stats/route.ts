import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';
import User from '@/models/User';

// GET - Get user statistics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    await connectDB();

    // Get user info
    const user = await User.findOne({ firebaseUid: userId });
    
    // Count user's images
    const totalImages = await Image.countDocuments({ userId });
    const publicImages = await Image.countDocuments({ userId, isPublic: true });
    const privateImages = totalImages - publicImages;

    // Get total likes on user's images
    const userImages = await Image.find({ userId }).select('likes');
    const totalLikes = userImages.reduce((sum, img) => sum + img.likes.length, 0);

    // Update user's total generations if it's different
    if (user && user.totalGenerations !== totalImages) {
      await User.findOneAndUpdate(
        { firebaseUid: userId },
        { totalGenerations: totalImages }
      );
    }

    return NextResponse.json({
      totalImages,
      publicImages,
      privateImages,
      totalLikes,
      user: user ? {
        displayName: user.displayName,
        photoURL: user.photoURL,
        username: user.username,
        bio: user.bio,
        followers: user.followers?.length || 0,
        following: user.following?.length || 0,
      } : null
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
