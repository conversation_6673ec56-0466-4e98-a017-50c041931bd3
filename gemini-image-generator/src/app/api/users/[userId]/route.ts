import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';
import User from '@/models/User';
import Comment from '@/models/Comment';
import Collection from '@/models/Collection';

// DELETE - Delete user account and all associated data
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    await connectDB();

    // Delete all user's images
    await Image.deleteMany({ userId });

    // Delete all user's comments
    await Comment.deleteMany({ userId });

    // Delete all user's collections
    await Collection.deleteMany({ userId });

    // Remove user from other users' followers/following lists
    await User.updateMany(
      { $or: [{ followers: userId }, { following: userId }] },
      { 
        $pull: { 
          followers: userId,
          following: userId 
        } 
      }
    );

    // Remove user's likes from all images
    await Image.updateMany(
      { likes: userId },
      { $pull: { likes: userId } }
    );

    // Delete the user record
    await User.deleteOne({ firebaseUid: userId });

    return NextResponse.json({
      success: true,
      message: 'User account and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user account:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
