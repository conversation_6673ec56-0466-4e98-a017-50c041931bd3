import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Follow from '@/models/Follow';
import User from '@/models/User';

// POST /api/users/[userId]/follow - Follow/unfollow user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await connectDB();

    const { followerId } = await request.json();
    const { userId } = await params;

    if (!followerId) {
      return NextResponse.json(
        { error: 'Follower ID required' },
        { status: 400 }
      );
    }

    if (followerId === userId) {
      return NextResponse.json(
        { error: 'Cannot follow yourself' },
        { status: 400 }
      );
    }

    // Get user info
    const [follower, following] = await Promise.all([
      User.findOne({ firebaseUid: followerId }),
      User.findOne({ firebaseUid: userId })
    ]);

    if (!follower || !following) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if already following
    const existingFollow = await Follow.findOne({
      followerId: followerId,
      followingId: userId
    });

    let isFollowing = false;

    if (existingFollow) {
      // Unfollow
      await Follow.findByIdAndDelete(existingFollow._id);
      isFollowing = false;
    } else {
      // Follow
      const newFollow = new Follow({
        followerId: followerId,
        followingId: userId,
        followerName: follower.displayName || follower.email,
        followingName: following.displayName || following.email,
      });
      await newFollow.save();
      isFollowing = true;
    }

    // Get updated counts
    const [followerCount, followingCount] = await Promise.all([
      Follow.countDocuments({ followingId: userId }),
      Follow.countDocuments({ followerId: userId })
    ]);

    return NextResponse.json({
      success: true,
      isFollowing,
      followerCount,
      followingCount,
      message: isFollowing ? 'Now following user' : 'Unfollowed user',
    });
  } catch (error) {
    console.error('Error following/unfollowing user:', error);
    return NextResponse.json(
      { error: 'Failed to follow/unfollow user' },
      { status: 500 }
    );
  }
}

// GET /api/users/[userId]/follow - Get follow status and counts
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const currentUserId = searchParams.get('currentUserId');
    const { userId } = await params;

    // Get follow counts
    const [followerCount, followingCount] = await Promise.all([
      Follow.countDocuments({ followingId: userId }),
      Follow.countDocuments({ followerId: userId })
    ]);

    let isFollowing = false;
    if (currentUserId && currentUserId !== userId) {
      const followRelation = await Follow.findOne({
        followerId: currentUserId,
        followingId: userId
      });
      isFollowing = !!followRelation;
    }

    return NextResponse.json({
      success: true,
      followerCount,
      followingCount,
      isFollowing,
    });
  } catch (error) {
    console.error('Error getting follow status:', error);
    return NextResponse.json(
      { error: 'Failed to get follow status' },
      { status: 500 }
    );
  }
}
