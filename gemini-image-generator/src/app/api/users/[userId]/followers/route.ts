import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Follow from '@/models/Follow';
import User from '@/models/User';

// GET /api/users/[userId]/followers - Get user's followers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const { userId } = await params;

    // Get followers
    const followers = await Follow.find({ followingId: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get follower user details
    const followerIds = followers.map(f => f.followerId);
    const followerUsers = await User.find({ 
      firebaseUid: { $in: followerIds } 
    }).lean();

    // Combine follow data with user data
    const followersWithDetails = followers.map(follow => {
      const user = followerUsers.find(u => u.firebaseUid === follow.followerId);
      return {
        ...follow,
        user: user ? {
          firebaseUid: user.firebaseUid,
          displayName: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          bio: user.bio,
        } : null,
      };
    });

    const total = await Follow.countDocuments({ followingId: userId });

    return NextResponse.json({
      success: true,
      followers: followersWithDetails,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching followers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch followers' },
      { status: 500 }
    );
  }
}
