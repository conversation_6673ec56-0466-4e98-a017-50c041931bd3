import { NextRequest, NextResponse } from 'next/server';
import { getRateLimitStatus } from '@/lib/rateLimit';

// GET - Check rate limit status for a user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const rateLimitStatus = await getRateLimitStatus(userId);

    return NextResponse.json({
      success: true,
      ...rateLimitStatus,
      limit: 5,
    });
  } catch (error) {
    console.error('Error checking rate limit:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
