import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI, Modality } from '@google/genai';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';
import { checkAndIncrementRateLimit } from '@/lib/rateLimit';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    const { prompt, style, mood, colorPalette, isPublic = false, userId } = await request.json();

    // Clean empty strings to null for proper validation
    const cleanStyle = style && style.trim() !== '' ? style : null;
    const cleanMood = mood && mood.trim() !== '' ? mood : null;

    // Check rate limit first
    if (userId) {
      const rateLimitResult = await checkAndIncrementRateLimit(userId);

      if (!rateLimitResult.allowed) {
        return NextResponse.json({
          error: `Daily limit of 5 images reached. You have generated ${rateLimitResult.current} images today. Limit resets at midnight.`,
          rateLimitExceeded: true,
          resetTime: rateLimitResult.resetTime,
          current: rateLimitResult.current,
          limit: 5,
        }, { status: 429 });
      }
    }

    if (!prompt || !prompt.trim()) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 401 }
      );
    }

    // Get API key from environment
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    await connectDB();

    const startTime = Date.now();
    const ai = new GoogleGenAI({ apiKey });

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash-preview-image-generation",
      contents: prompt,
      config: {
        responseModalities: [Modality.TEXT, Modality.IMAGE],
      },
    });

    const generationTime = Date.now() - startTime;

    // Find the image data in the response
    if (!response.candidates?.[0]?.content?.parts) {
      return NextResponse.json(
        { error: 'Invalid response format from Gemini API' },
        { status: 500 }
      );
    }

    for (const part of response.candidates[0].content.parts) {
      if (part.inlineData) {
        const imageData = part.inlineData.data;
        const imageUrl = `data:image/png;base64,${imageData}`;

        // Save to MongoDB
        const newImage = new Image({
          userId: userId,
          prompt,
          imageUrl,
          imageData,
          style: cleanStyle,
          mood: cleanMood,
          colorPalette,
          isPublic,
          generationTime,
          metadata: {
            model: "gemini-2.0-flash-preview-image-generation",
            timestamp: new Date(),
          },
        });

        await newImage.save();

        // Update user's total generations count
        await User.findOneAndUpdate(
          { firebaseUid: userId },
          { $inc: { totalGenerations: 1 } }
        );

        return NextResponse.json({
          success: true,
          image: {
            _id: newImage._id,
            imageUrl,
            prompt,
            style: cleanStyle,
            mood: cleanMood,
            colorPalette,
            generationTime,
            createdAt: newImage.createdAt,
          },
        });
      }
    }

    // If no image was found in the response
    return NextResponse.json(
      { error: 'No image was generated in the response' },
      { status: 500 }
    );

  } catch (error: unknown) {
    console.error('Error generating image:', error);
    
    // Handle specific error types
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    if (errorMessage.includes('API key') || errorMessage.includes('401')) {
      return NextResponse.json(
        { error: 'Invalid API key. Please check your Gemini API key and ensure it has access to image generation.' },
        { status: 401 }
      );
    }

    if (errorMessage.includes('quota') || errorMessage.includes('limit') || errorMessage.includes('429')) {
      return NextResponse.json(
        { error: 'API quota exceeded. Please try again later or check your billing settings.' },
        { status: 429 }
      );
    }

    if (errorMessage.includes('region') || errorMessage.includes('location')) {
      return NextResponse.json(
        { error: 'Image generation may not be available in your region. Please check the Gemini API documentation.' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: errorMessage || 'Failed to generate image' },
      { status: 500 }
    );
  }
}
