import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Follow from '@/models/Follow';
import Image from '@/models/Image';

// GET /api/feed - Get personalized feed for user
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Get users that this user follows
    const following = await Follow.find({ followerId: userId }).lean();
    const followingIds = following.map(f => f.followingId);

    // Include user's own images in the feed
    const userIds = [...followingIds, userId];

    if (userIds.length === 0) {
      return NextResponse.json({
        success: true,
        images: [],
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0,
        },
      });
    }

    // Get images from followed users and own images
    const [images, total] = await Promise.all([
      Image.find({
        userId: { $in: userIds },
        isPublic: true,
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
      Image.countDocuments({
        userId: { $in: userIds },
        isPublic: true,
      })
    ]);

    // Add user info to each image
    const imagesWithUserInfo = images.map(image => ({
      ...image,
      isFromFollowedUser: followingIds.includes(image.userId),
      isOwnImage: image.userId === userId,
    }));

    return NextResponse.json({
      success: true,
      images: imagesWithUserInfo,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching feed:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feed' },
      { status: 500 }
    );
  }
}
