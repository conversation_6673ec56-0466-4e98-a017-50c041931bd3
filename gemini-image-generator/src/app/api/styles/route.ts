import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomStyle from '@/models/CustomStyle';
import User from '@/models/User';

// GET /api/styles - Get public custom styles
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const sort = searchParams.get('sort') || 'recent';
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    // Build query
    const query: any = {
      isPublic: true,
      isApproved: true,
    };

    if (category && category !== 'all') {
      query.category = category;
    }

    if (featured === 'true') {
      query.isFeatured = true;
    }

    // Build sort
    let sortQuery: any = {};
    switch (sort) {
      case 'popular':
        sortQuery = { downloads: -1, rating: -1 };
        break;
      case 'rating':
        sortQuery = { rating: -1, ratingCount: -1 };
        break;
      case 'recent':
      default:
        sortQuery = { createdAt: -1 };
        break;
    }

    const skip = (page - 1) * limit;

    const [styles, total] = await Promise.all([
      CustomStyle.find(query)
        .sort(sortQuery)
        .skip(skip)
        .limit(limit)
        .lean(),
      CustomStyle.countDocuments(query)
    ]);

    return NextResponse.json({
      success: true,
      styles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching custom styles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch custom styles' },
      { status: 500 }
    );
  }
}

// POST /api/styles - Create new custom style
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      description,
      basePrompt,
      examples,
      tags,
      category,
      userId,
    } = body;

    // Validate required fields
    if (!name || !description || !basePrompt || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get user info
    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has reached style creation limit (e.g., 10 styles)
    const userStyleCount = await CustomStyle.countDocuments({ createdBy: userId });
    if (userStyleCount >= 10) {
      return NextResponse.json(
        { error: 'Style creation limit reached (10 styles maximum)' },
        { status: 429 }
      );
    }

    // Create new style
    const newStyle = new CustomStyle({
      name: name.trim(),
      description: description.trim(),
      basePrompt: basePrompt.trim(),
      createdBy: userId,
      creatorName: user.displayName || user.email,
      examples: examples || [],
      tags: tags || [],
      category: category || 'artistic',
      isPublic: false, // Starts as private, user can make public later
      moderationStatus: 'pending',
    });

    await newStyle.save();

    return NextResponse.json({
      success: true,
      style: newStyle,
    });
  } catch (error) {
    console.error('Error creating custom style:', error);
    return NextResponse.json(
      { error: 'Failed to create custom style' },
      { status: 500 }
    );
  }
}
