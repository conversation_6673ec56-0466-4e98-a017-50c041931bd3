import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomStyle from '@/models/CustomStyle';

// POST /api/styles/[id]/like - Toggle like on style
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { userId } = await request.json();
    const { id } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    const style = await CustomStyle.findById(id);
    if (!style) {
      return NextResponse.json(
        { error: 'Style not found' },
        { status: 404 }
      );
    }

    const hasLiked = style.likes.includes(userId);
    let updatedStyle;

    if (hasLiked) {
      // Remove like
      updatedStyle = await CustomStyle.findByIdAndUpdate(
        id,
        { $pull: { likes: userId } },
        { new: true }
      );
    } else {
      // Add like
      updatedStyle = await CustomStyle.findByIdAndUpdate(
        id,
        { $addToSet: { likes: userId } },
        { new: true }
      );
    }

    return NextResponse.json({
      success: true,
      liked: !hasLiked,
      likeCount: updatedStyle.likes.length,
    });
  } catch (error) {
    console.error('Error toggling style like:', error);
    return NextResponse.json(
      { error: 'Failed to toggle like' },
      { status: 500 }
    );
  }
}
