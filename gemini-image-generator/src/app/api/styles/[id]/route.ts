import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomStyle from '@/models/CustomStyle';

// GET /api/styles/[id] - Get specific style
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const style = await CustomStyle.findById(id).lean();
    
    if (!style) {
      return NextResponse.json(
        { error: 'Style not found' },
        { status: 404 }
      );
    }

    // Increment usage count
    await CustomStyle.findByIdAndUpdate(id, {
      $inc: { usageCount: 1 }
    });

    return NextResponse.json({
      success: true,
      style,
    });
  } catch (error) {
    console.error('Error fetching style:', error);
    return NextResponse.json(
      { error: 'Failed to fetch style' },
      { status: 500 }
    );
  }
}

// PUT /api/styles/[id] - Update style
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const body = await request.json();
    const { userId, ...updateData } = body;
    const { id } = await params;

    // Find the style
    const style = await CustomStyle.findById(id);
    if (!style) {
      return NextResponse.json(
        { error: 'Style not found' },
        { status: 404 }
      );
    }

    // Check if user owns the style
    if (style.createdBy !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update the style
    const updatedStyle = await CustomStyle.findByIdAndUpdate(
      id,
      {
        ...updateData,
        // Reset moderation status if making public
        ...(updateData.isPublic && { moderationStatus: 'pending' })
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      style: updatedStyle,
    });
  } catch (error) {
    console.error('Error updating style:', error);
    return NextResponse.json(
      { error: 'Failed to update style' },
      { status: 500 }
    );
  }
}

// DELETE /api/styles/[id] - Delete style
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const { id } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Find the style
    const style = await CustomStyle.findById(id);
    if (!style) {
      return NextResponse.json(
        { error: 'Style not found' },
        { status: 404 }
      );
    }

    // Check if user owns the style
    if (style.createdBy !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete the style
    await CustomStyle.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Style deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting style:', error);
    return NextResponse.json(
      { error: 'Failed to delete style' },
      { status: 500 }
    );
  }
}
