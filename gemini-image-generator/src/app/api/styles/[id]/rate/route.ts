import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import CustomStyle from '@/models/CustomStyle';
import mongoose from 'mongoose';

// Rating model for tracking individual ratings
const RatingSchema = new mongoose.Schema({
  styleId: { type: mongoose.Schema.Types.ObjectId, ref: 'CustomStyle', required: true },
  userId: { type: String, required: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
}, {
  timestamps: true,
});

RatingSchema.index({ styleId: 1, userId: 1 }, { unique: true });

const Rating = mongoose.models.Rating || mongoose.model('Rating', RatingSchema);

// POST /api/styles/[id]/rate - Rate a style
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { userId, rating } = await request.json();
    const { id } = await params;

    if (!userId || !rating) {
      return NextResponse.json(
        { error: 'User ID and rating required' },
        { status: 400 }
      );
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    const style = await CustomStyle.findById(id);
    if (!style) {
      return NextResponse.json(
        { error: 'Style not found' },
        { status: 404 }
      );
    }

    // Check if user is trying to rate their own style
    if (style.createdBy === userId) {
      return NextResponse.json(
        { error: 'Cannot rate your own style' },
        { status: 400 }
      );
    }

    // Upsert the rating
    await Rating.findOneAndUpdate(
      { styleId: id, userId },
      { rating },
      { upsert: true, new: true }
    );

    // Recalculate average rating
    const ratings = await Rating.find({ styleId: id });
    const averageRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;

    // Update style with new rating
    const updatedStyle = await CustomStyle.findByIdAndUpdate(
      id,
      {
        rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        ratingCount: ratings.length,
      },
      { new: true }
    );

    // Check if style should be featured (rating >= 4.5 and >= 10 ratings)
    if (updatedStyle.rating >= 4.5 && updatedStyle.ratingCount >= 10) {
      await CustomStyle.findByIdAndUpdate(id, { isFeatured: true });
    }

    return NextResponse.json({
      success: true,
      rating: updatedStyle.rating,
      ratingCount: updatedStyle.ratingCount,
      userRating: rating,
    });
  } catch (error) {
    console.error('Error rating style:', error);
    return NextResponse.json(
      { error: 'Failed to rate style' },
      { status: 500 }
    );
  }
}

// GET /api/styles/[id]/rate - Get user's rating for a style
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const { id } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    const userRating = await Rating.findOne({
      styleId: id,
      userId,
    });

    return NextResponse.json({
      success: true,
      userRating: userRating?.rating || null,
    });
  } catch (error) {
    console.error('Error fetching user rating:', error);
    return NextResponse.json(
      { error: 'Failed to fetch rating' },
      { status: 500 }
    );
  }
}
