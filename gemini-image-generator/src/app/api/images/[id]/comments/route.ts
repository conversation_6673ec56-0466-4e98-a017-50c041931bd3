import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Comment from '@/models/Comment';
import User from '@/models/User';
import Image from '@/models/Image';

// GET - Fetch comments for an image
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: imageId } = await params;

    await connectDB();

    // Get comments with user information
    const comments = await Comment.find({ imageId, parentComment: null })
      .sort({ createdAt: -1 })
      .lean();

    // Get user information for each comment
    const userIds = [...new Set(comments.map(comment => comment.userId))];
    const users = await User.find({ firebaseUid: { $in: userIds } })
      .select('firebaseUid displayName photoURL username')
      .lean();

    // Create a map for quick user lookup
    const userMap = users.reduce((acc, user) => {
      acc[user.firebaseUid] = user;
      return acc;
    }, {} as Record<string, any>);

    // Get replies for each comment
    const commentsWithReplies = await Promise.all(
      comments.map(async (comment) => {
        const replies = await Comment.find({ parentComment: comment._id })
          .sort({ createdAt: 1 })
          .lean();

        const replyUserIds = [...new Set(replies.map(reply => reply.userId))];
        const replyUsers = await User.find({ firebaseUid: { $in: replyUserIds } })
          .select('firebaseUid displayName photoURL username')
          .lean();

        const replyUserMap = replyUsers.reduce((acc, user) => {
          acc[user.firebaseUid] = user;
          return acc;
        }, {} as Record<string, any>);

        const repliesWithUsers = replies.map(reply => ({
          ...reply,
          user: replyUserMap[reply.userId] || {
            displayName: 'Anonymous',
            photoURL: null,
            username: 'anonymous'
          }
        }));

        return {
          ...comment,
          user: userMap[comment.userId] || {
            displayName: 'Anonymous',
            photoURL: null,
            username: 'anonymous'
          },
          replies: repliesWithUsers
        };
      })
    );

    return NextResponse.json(commentsWithReplies);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Add a comment to an image
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: imageId } = await params;
    const { content, userId, parentComment } = await request.json();

    if (!content || !content.trim()) {
      return NextResponse.json({ error: 'Comment content is required' }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 401 });
    }

    if (content.length > 1000) {
      return NextResponse.json({ error: 'Comment is too long (max 1000 characters)' }, { status: 400 });
    }

    await connectDB();

    // Check if image exists
    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Create new comment
    const newComment = new Comment({
      userId,
      imageId,
      content: content.trim(),
      parentComment: parentComment || null,
    });

    await newComment.save();

    // Add comment ID to image's comments array
    await Image.findByIdAndUpdate(
      imageId,
      { $addToSet: { comments: newComment._id } }
    );

    // Get user information for the response
    const user = await User.findOne({ firebaseUid: userId })
      .select('firebaseUid displayName photoURL username')
      .lean();

    const commentWithUser = {
      ...newComment.toObject(),
      user: user || {
        displayName: 'Anonymous',
        photoURL: null,
        username: 'anonymous'
      },
      replies: []
    };

    return NextResponse.json({
      success: true,
      comment: commentWithUser
    });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
