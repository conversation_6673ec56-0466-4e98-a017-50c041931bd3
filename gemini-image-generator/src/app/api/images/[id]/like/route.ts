import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';

// POST - Like/Unlike an image
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await request.json();
    const { id: imageId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 401 });
    }

    await connectDB();

    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    const isLiked = image.likes.includes(userId);

    let updatedImage;
    if (isLiked) {
      // Unlike the image - remove user from likes array
      updatedImage = await Image.findByIdAndUpdate(
        imageId,
        { $pull: { likes: userId } },
        { new: true }
      );
    } else {
      // Like the image - add user to likes array (only if not already present)
      updatedImage = await Image.findByIdAndUpdate(
        imageId,
        { $addToSet: { likes: userId } }, // $addToSet ensures uniqueness
        { new: true }
      );
    }

    return NextResponse.json({
      success: true,
      isLiked: !isLiked,
      likesCount: updatedImage?.likes.length || 0,
    });
  } catch (error) {
    console.error('Error liking image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
