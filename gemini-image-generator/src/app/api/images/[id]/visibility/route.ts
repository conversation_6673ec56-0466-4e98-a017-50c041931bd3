import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';

// PUT - Toggle image visibility (public/private)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { isPublic, userId } = await request.json();
    const { id: imageId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 401 });
    }

    await connectDB();

    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Check if user owns the image
    if (image.userId !== userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    image.isPublic = isPublic;
    await image.save();

    return NextResponse.json({
      success: true,
      isPublic: image.isPublic,
    });
  } catch (error) {
    console.error('Error updating image visibility:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
