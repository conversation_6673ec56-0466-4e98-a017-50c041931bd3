import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';
import User from '@/models/User';

// GET - Fetch public images from all users
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const style = searchParams.get('style');
    const mood = searchParams.get('mood');
    const skip = (page - 1) * limit;

    await connectDB();

    // Build query for public images
    const query: any = { isPublic: true };
    
    if (style) {
      query.style = style;
    }
    
    if (mood) {
      query.mood = mood;
    }

    // Get public images with user information
    const images = await Image.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-imageData') // Exclude base64 data for list view
      .lean();

    // Get user information for each image
    const userIds = [...new Set(images.map(img => img.userId))];
    const users = await User.find({ firebaseUid: { $in: userIds } })
      .select('firebaseUid displayName photoURL username')
      .lean();

    // Create a map for quick user lookup
    const userMap = users.reduce((acc, user) => {
      acc[user.firebaseUid] = user;
      return acc;
    }, {} as Record<string, any>);

    // Add user information to images
    const imagesWithUsers = images.map(image => ({
      ...image,
      user: userMap[image.userId] || {
        displayName: 'Anonymous',
        photoURL: null,
        username: 'anonymous'
      }
    }));

    const total = await Image.countDocuments(query);

    return NextResponse.json({
      images: imagesWithUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching public images:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
