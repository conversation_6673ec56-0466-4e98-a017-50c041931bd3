import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Image from '@/models/Image';

// GET - Fetch user's images
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    await connectDB();

    const images = await Image.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-imageData'); // Exclude base64 data for list view

    // const total = await Image.countDocuments({ userId });

    return NextResponse.json(images);
  } catch (error) {
    console.error('Error fetching user images:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
