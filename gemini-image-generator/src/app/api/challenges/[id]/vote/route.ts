import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Challenge from '@/models/Challenge';
import ChallengeSubmission from '@/models/ChallengeSubmission';

// POST /api/challenges/[id]/vote - Vote on challenge submissions
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const body = await request.json();
    const { submissionId, userId } = body;
    const { id } = await params;

    if (!submissionId || !userId) {
      return NextResponse.json(
        { error: 'Submission ID and User ID required' },
        { status: 400 }
      );
    }

    // Find the challenge
    const challenge = await Challenge.findById(id);
    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      );
    }

    // Update challenge status
    challenge.updateStatus();
    await challenge.save();

    // Check if challenge is in voting phase
    if (challenge.status !== 'voting') {
      return NextResponse.json(
        { error: 'Challenge is not currently in voting phase' },
        { status: 400 }
      );
    }

    // Find the submission
    const submission = await ChallengeSubmission.findById(submissionId);
    if (!submission) {
      return NextResponse.json(
        { error: 'Submission not found' },
        { status: 404 }
      );
    }

    // Check if submission belongs to this challenge
    if (submission.challengeId.toString() !== id) {
      return NextResponse.json(
        { error: 'Submission does not belong to this challenge' },
        { status: 400 }
      );
    }

    // Check if user is trying to vote for their own submission
    if (submission.userId === userId) {
      return NextResponse.json(
        { error: 'Cannot vote for your own submission' },
        { status: 400 }
      );
    }

    // Check if user has already voted for this submission
    const hasVoted = submission.hasUserVoted(userId);
    let voted = false;

    if (hasVoted) {
      // Remove vote
      submission.removeVote(userId);
      voted = false;
    } else {
      // Add vote
      submission.addVote(userId);
      voted = true;
    }

    await submission.save();

    return NextResponse.json({
      success: true,
      voted: voted,
      voteCount: submission.voteCount,
      message: voted ? 'Vote added!' : 'Vote removed!',
    });
  } catch (error) {
    console.error('Error voting on submission:', error);
    return NextResponse.json(
      { error: 'Failed to vote on submission' },
      { status: 500 }
    );
  }
}

// GET /api/challenges/[id]/vote - Get user's votes for challenge
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const { id } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Get all submissions for this challenge that the user has voted on
    const submissions = await ChallengeSubmission.find({
      challengeId: id,
      'votes.userId': userId
    }).select('_id').lean();

    const votedSubmissionIds = submissions.map((sub: any) => sub._id.toString());

    return NextResponse.json({
      success: true,
      votedSubmissions: votedSubmissionIds,
    });
  } catch (error) {
    console.error('Error fetching user votes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user votes' },
      { status: 500 }
    );
  }
}
