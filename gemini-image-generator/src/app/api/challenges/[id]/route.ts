import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Challenge from '@/models/Challenge';
import ChallengeSubmission from '@/models/ChallengeSubmission';
import Image from '@/models/Image';

// GET /api/challenges/[id] - Get specific challenge with submissions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const includeSubmissions = searchParams.get('includeSubmissions') !== 'false';
    const userId = searchParams.get('userId');
    const { id } = await params;

    const challenge = await Challenge.findById(id).lean();
    
    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      );
    }

    // Update challenge status
    const challengeDoc = await Challenge.findById(id);
    challengeDoc.updateStatus();
    await challengeDoc.save();

    let submissions: any[] = [];
    let userSubmission: any = null;

    if (includeSubmissions) {
      // Get all approved submissions with image data
      submissions = await ChallengeSubmission.find({
        challengeId: id,
        moderationStatus: 'approved'
      })
      .populate('imageId')
      .sort({ voteCount: -1, createdAt: 1 })
      .lean();

      // Get user's submission if userId provided
      if (userId) {
        userSubmission = await ChallengeSubmission.findOne({
          challengeId: id,
          userId: userId
        }).populate('imageId').lean();
      }
    }

    return NextResponse.json({
      success: true,
      challenge: {
        ...challenge,
        status: challengeDoc.status,
      },
      submissions,
      userSubmission,
    });
  } catch (error) {
    console.error('Error fetching challenge:', error);
    return NextResponse.json(
      { error: 'Failed to fetch challenge' },
      { status: 500 }
    );
  }
}

// PUT /api/challenges/[id] - Update challenge (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const body = await request.json();
    const { userId, ...updateData } = body;
    const { id } = await params;

    // Find the challenge
    const challenge = await Challenge.findById(id);
    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      );
    }

    // Check if user is the creator or admin (simplified check)
    if (challenge.createdBy !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update the challenge
    const updatedChallenge = await Challenge.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );

    return NextResponse.json({
      success: true,
      challenge: updatedChallenge,
    });
  } catch (error) {
    console.error('Error updating challenge:', error);
    return NextResponse.json(
      { error: 'Failed to update challenge' },
      { status: 500 }
    );
  }
}

// DELETE /api/challenges/[id] - Delete challenge (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const { id } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Find the challenge
    const challenge = await Challenge.findById(id);
    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      );
    }

    // Check if user is the creator or admin
    if (challenge.createdBy !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete all submissions for this challenge
    await ChallengeSubmission.deleteMany({ challengeId: id });

    // Delete the challenge
    await Challenge.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Challenge deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting challenge:', error);
    return NextResponse.json(
      { error: 'Failed to delete challenge' },
      { status: 500 }
    );
  }
}
