import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Challenge from '@/models/Challenge';
import ChallengeSubmission from '@/models/ChallengeSubmission';
import Image from '@/models/Image';
import User from '@/models/User';

// POST /api/challenges/[id]/submit - Submit to challenge
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const body = await request.json();
    const { imageId, userId, title, description, technique } = body;
    const { id } = await params;

    if (!imageId || !userId) {
      return NextResponse.json(
        { error: 'Image ID and User ID required' },
        { status: 400 }
      );
    }

    // Find the challenge
    const challenge = await Challenge.findById(id);
    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      );
    }

    // Update challenge status
    challenge.updateStatus();
    await challenge.save();

    // Check if challenge is active
    if (challenge.status !== 'active') {
      return NextResponse.json(
        { error: 'Challenge is not currently accepting submissions' },
        { status: 400 }
      );
    }

    // Check if user already submitted
    const existingSubmission = await ChallengeSubmission.findOne({
      challengeId: id,
      userId: userId
    });

    if (existingSubmission) {
      return NextResponse.json(
        { error: 'You have already submitted to this challenge' },
        { status: 400 }
      );
    }

    // Verify the image exists and belongs to the user
    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    if (image.userId !== userId) {
      return NextResponse.json(
        { error: 'You can only submit your own images' },
        { status: 403 }
      );
    }

    // Get user info
    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get submission order (for tiebreaking)
    const submissionCount = await ChallengeSubmission.countDocuments({
      challengeId: id
    });

    // Create submission
    const submission = new ChallengeSubmission({
      challengeId: id,
      imageId: imageId,
      userId: userId,
      userName: user.displayName || user.email,
      title: title?.trim() || '',
      description: description?.trim() || '',
      technique: technique?.trim() || '',
      submissionOrder: submissionCount + 1,
      moderationStatus: 'approved', // Auto-approve for now
    });

    await submission.save();

    // Update challenge participant count
    const participantCount = await ChallengeSubmission.distinct('userId', {
      challengeId: id,
      moderationStatus: 'approved'
    }).then(users => users.length);

    await Challenge.findByIdAndUpdate(id, {
      participantCount: participantCount,
      submissionCount: submissionCount + 1,
    });

    // Make the image public if it isn't already
    if (!image.isPublic) {
      await Image.findByIdAndUpdate(imageId, { isPublic: true });
    }

    return NextResponse.json({
      success: true,
      submission: submission,
      message: 'Successfully submitted to challenge!',
    });
  } catch (error) {
    console.error('Error submitting to challenge:', error);
    return NextResponse.json(
      { error: 'Failed to submit to challenge' },
      { status: 500 }
    );
  }
}
