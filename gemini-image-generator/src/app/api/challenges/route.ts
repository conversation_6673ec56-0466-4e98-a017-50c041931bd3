import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Challenge from '@/models/Challenge';
import ChallengeSubmission from '@/models/ChallengeSubmission';

// GET /api/challenges - Get challenges
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'active';
    const featured = searchParams.get('featured');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');

    // Build query
    const query: any = {};
    
    if (status !== 'all') {
      query.status = status;
    }

    if (featured === 'true') {
      query.isFeatured = true;
    }

    // Update challenge statuses based on current date
    const allChallenges = await Challenge.find({});
    for (const challenge of allChallenges) {
      const oldStatus = challenge.status;
      challenge.updateStatus();
      if (oldStatus !== challenge.status) {
        await challenge.save();
      }
    }

    const skip = (page - 1) * limit;

    const [challenges, total] = await Promise.all([
      Challenge.find(query)
        .sort({ isFeatured: -1, startDate: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Challenge.countDocuments(query)
    ]);

    // Get submission counts for each challenge
    const challengesWithCounts = await Promise.all(
      challenges.map(async (challenge) => {
        const submissionCount = await ChallengeSubmission.countDocuments({
          challengeId: challenge._id,
          moderationStatus: 'approved'
        });
        
        const participantCount = await ChallengeSubmission.distinct('userId', {
          challengeId: challenge._id,
          moderationStatus: 'approved'
        }).then(users => users.length);

        return {
          ...challenge,
          submissionCount,
          participantCount,
        };
      })
    );

    return NextResponse.json({
      success: true,
      challenges: challengesWithCounts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching challenges:', error);
    return NextResponse.json(
      { error: 'Failed to fetch challenges' },
      { status: 500 }
    );
  }
}

// POST /api/challenges - Create new challenge (admin only)
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      title,
      description,
      theme,
      hashtag,
      type,
      category,
      difficulty,
      promptSuggestions,
      rules,
      prizes,
      startDate,
      endDate,
      votingEndDate,
      maxSubmissions,
      userId,
    } = body;

    // Validate required fields
    if (!title || !description || !theme || !hashtag || !startDate || !endDate || !votingEndDate || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    const votingEnd = new Date(votingEndDate);

    if (start >= end || end >= votingEnd) {
      return NextResponse.json(
        { error: 'Invalid date sequence. Start < End < Voting End' },
        { status: 400 }
      );
    }

    // Check if hashtag already exists
    const existingChallenge = await Challenge.findOne({ hashtag: hashtag.toLowerCase() });
    if (existingChallenge) {
      return NextResponse.json(
        { error: 'Hashtag already exists' },
        { status: 400 }
      );
    }

    // Create new challenge
    const newChallenge = new Challenge({
      title: title.trim(),
      description: description.trim(),
      theme: theme.trim(),
      hashtag: hashtag.toLowerCase().replace(/[^a-z0-9]/g, ''),
      type: type || 'weekly',
      category: category || 'theme',
      difficulty: difficulty || 'intermediate',
      promptSuggestions: promptSuggestions || [],
      rules: rules || [],
      prizes: prizes || [],
      startDate: start,
      endDate: end,
      votingEndDate: votingEnd,
      maxSubmissions: maxSubmissions || 1,
      createdBy: userId,
    });

    // Set initial status
    newChallenge.updateStatus();
    await newChallenge.save();

    return NextResponse.json({
      success: true,
      challenge: newChallenge,
    });
  } catch (error) {
    console.error('Error creating challenge:', error);
    return NextResponse.json(
      { error: 'Failed to create challenge' },
      { status: 500 }
    );
  }
}
