import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Subscription from '@/models/Subscription';
import User from '@/models/User';

// GET /api/subscription - Get user's subscription
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Get or create subscription
    let subscription = await Subscription.findOne({ userId });
    
    if (!subscription) {
      // Create default free subscription
      subscription = new Subscription({
        userId,
        tier: 'free',
        status: 'active',
        features: Subscription.getTierFeatures('free'),
      });
      await subscription.save();
    }

    // Reset monthly usage if needed
    subscription.resetMonthlyUsage();
    await subscription.save();

    return NextResponse.json({
      success: true,
      subscription,
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
}

// POST /api/subscription - Create or update subscription
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { userId, tier, stripeData } = body;

    if (!userId || !tier) {
      return NextResponse.json(
        { error: 'User ID and tier required' },
        { status: 400 }
      );
    }

    // Verify user exists
    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get tier features
    const features = Subscription.getTierFeatures(tier);

    // Update or create subscription
    const subscription = await Subscription.findOneAndUpdate(
      { userId },
      {
        tier,
        features,
        status: 'active',
        ...(stripeData && {
          stripeCustomerId: stripeData.customerId,
          stripeSubscriptionId: stripeData.subscriptionId,
          stripePriceId: stripeData.priceId,
          currentPeriodStart: stripeData.currentPeriodStart,
          currentPeriodEnd: stripeData.currentPeriodEnd,
          billing: {
            amount: stripeData.amount,
            currency: stripeData.currency,
            interval: stripeData.interval,
          },
        }),
      },
      { 
        new: true, 
        upsert: true,
        setDefaultsOnInsert: true,
      }
    );

    return NextResponse.json({
      success: true,
      subscription,
      message: `Successfully upgraded to ${tier} plan!`,
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
}

// DELETE /api/subscription - Cancel subscription
export async function DELETE(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Update subscription to cancelled
    const subscription = await Subscription.findOneAndUpdate(
      { userId },
      {
        status: 'cancelled',
        cancelAtPeriodEnd: true,
      },
      { new: true }
    );

    if (!subscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      subscription,
      message: 'Subscription cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      { error: 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}
