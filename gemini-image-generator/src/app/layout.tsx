import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import EmailVerificationBanner from '@/components/EmailVerificationBanner';
import MobileBottomNav from '@/components/MobileBottomNav';
import { Toaster } from 'react-hot-toast';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI Art Studio - Create Stunning Visuals with AI",
  description: "Create professional AI art and share with a vibrant creative community. Powered by Google's cutting-edge Gemini 2.0 Flash technology.",
  keywords: "AI art, image generation, creative community, Gemini AI, digital art, AI studio",
  openGraph: {
    title: "AI Art Studio - Create Stunning Visuals with AI",
    description: "Join thousands of creators making amazing AI art",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
            <Header />
            <div className="container mx-auto px-3 sm:px-4">
              <EmailVerificationBanner />
            </div>
            <div className="pb-16 md:pb-0">
              {children}
            </div>

            <MobileBottomNav />

            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
