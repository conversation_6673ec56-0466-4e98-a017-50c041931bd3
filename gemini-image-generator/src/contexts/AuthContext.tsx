'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendEmailVerification,
  reload
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { IUser } from '@/models/User';

interface AuthContextType {
  user: User | null;
  userProfile: IUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (data: Partial<IUser>) => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  checkEmailVerified: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<IUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      setUserProfile(null); // Simplified - no MongoDB profile for now
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);

    // Check if email is verified
    if (!userCredential.user.emailVerified) {
      throw new Error('Please verify your email before signing in. Check your inbox for the verification link.');
    }
  };

  const signUp = async (email: string, password: string, displayName: string) => {
    const { user } = await createUserWithEmailAndPassword(auth, email, password);
    await updateProfile(user, { displayName });

    // Send email verification
    await sendEmailVerification(user, {
      url: `${window.location.origin}`, // Redirect URL after verification
      handleCodeInApp: false,
    });
  };

  const signInWithGoogle = async () => {
    const provider = new GoogleAuthProvider();

    try {
      // Try popup first
      await signInWithPopup(auth, provider);
    } catch (error: any) {
      // If popup is blocked or fails, try redirect
      if (error.code === 'auth/popup-blocked' ||
          error.code === 'auth/popup-closed-by-user' ||
          error.message?.includes('popup') ||
          error.message?.includes('Cross-Origin-Opener-Policy')) {
        const { signInWithRedirect } = await import('firebase/auth');
        await signInWithRedirect(auth, provider);
      } else {
        throw error;
      }
    }
  };

  const logout = async () => {
    await signOut(auth);
  };

  const updateUserProfile = async (_data: Partial<IUser>) => {
    // Simplified - no profile updates for now
    console.log('Profile update not implemented yet');
  };

  const sendVerificationEmail = async () => {
    if (!user) {
      throw new Error('No user logged in');
    }

    await sendEmailVerification(user, {
      url: `${window.location.origin}`, // Redirect URL after verification
      handleCodeInApp: false,
    });
  };

  const checkEmailVerified = async (): Promise<boolean> => {
    if (!user) {
      return false;
    }

    // Reload user to get latest emailVerified status
    await reload(user);
    return user.emailVerified;
  };

  const value = {
    user,
    userProfile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    logout,
    updateUserProfile,
    sendVerificationEmail,
    checkEmailVerified,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
