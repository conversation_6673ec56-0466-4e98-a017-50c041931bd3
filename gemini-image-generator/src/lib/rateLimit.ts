import RateLimit from '@/models/RateLimit';
import Subscription from '@/models/Subscription';
import connectDB from './mongodb';

export const DAILY_LIMIT = 5; // Default for free users

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  current: number;
  limit: number;
}

/**
 * Check if user can generate an image and increment counter if allowed
 */
export async function checkAndIncrementRateLimit(userId: string): Promise<RateLimitResult> {
  await connectDB();

  // Get user's subscription to determine their daily limit
  let subscription = await Subscription.findOne({ userId });
  if (!subscription) {
    // Create default free subscription
    subscription = new Subscription({
      userId,
      tier: 'free',
      features: Subscription.getTierFeatures('free'),
    });
    await subscription.save();
  }

  const dailyLimit = subscription.getDailyImageLimit();
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const resetTime = new Date();
  resetTime.setDate(resetTime.getDate() + 1);
  resetTime.setHours(0, 0, 0, 0); // Next midnight

  try {
    // Try to find existing rate limit record for today
    let rateLimitRecord = await RateLimit.findOne({ userId, date: today });

    if (!rateLimitRecord) {
      // Create new record for today
      rateLimitRecord = new RateLimit({
        userId,
        date: today,
        count: 1,
      });
      await rateLimitRecord.save();

      return {
        allowed: true,
        remaining: dailyLimit - 1,
        resetTime,
        current: 1,
        limit: dailyLimit,
      };
    }

    // Check if user has exceeded limit
    if (rateLimitRecord.count >= dailyLimit) {
      return {
        allowed: false,
        remaining: 0,
        resetTime,
        current: rateLimitRecord.count,
        limit: dailyLimit,
      };
    }

    // Increment counter
    rateLimitRecord.count += 1;
    await rateLimitRecord.save();

    return {
      allowed: true,
      remaining: dailyLimit - rateLimitRecord.count,
      resetTime,
      current: rateLimitRecord.count,
      limit: dailyLimit,
    };

  } catch (error) {
    console.error('Rate limit error:', error);
    // In case of error, allow the request but log it
    return {
      allowed: true,
      remaining: DAILY_LIMIT - 1,
      resetTime,
      current: 1,
      limit: DAILY_LIMIT,
    };
  }
}

/**
 * Get current rate limit status without incrementing
 */
export async function getRateLimitStatus(userId: string): Promise<RateLimitResult> {
  await connectDB();
  
  const today = new Date().toISOString().split('T')[0];
  const resetTime = new Date();
  resetTime.setDate(resetTime.getDate() + 1);
  resetTime.setHours(0, 0, 0, 0);
  
  try {
    const rateLimitRecord = await RateLimit.findOne({ userId, date: today });
    
    if (!rateLimitRecord) {
      return {
        allowed: true,
        remaining: DAILY_LIMIT,
        resetTime,
        current: 0,
      };
    }
    
    return {
      allowed: rateLimitRecord.count < DAILY_LIMIT,
      remaining: Math.max(0, DAILY_LIMIT - rateLimitRecord.count),
      resetTime,
      current: rateLimitRecord.count,
    };
    
  } catch (error) {
    console.error('Rate limit status error:', error);
    return {
      allowed: true,
      remaining: DAILY_LIMIT,
      resetTime,
      current: 0,
    };
  }
}

/**
 * Reset rate limit for a user (admin function)
 */
export async function resetUserRateLimit(userId: string): Promise<boolean> {
  await connectDB();
  
  const today = new Date().toISOString().split('T')[0];
  
  try {
    await RateLimit.deleteOne({ userId, date: today });
    return true;
  } catch (error) {
    console.error('Reset rate limit error:', error);
    return false;
  }
}

/**
 * Get rate limit statistics for admin dashboard
 */
export async function getRateLimitStats(): Promise<{
  totalUsers: number;
  usersAtLimit: number;
  totalGenerationsToday: number;
}> {
  await connectDB();
  
  const today = new Date().toISOString().split('T')[0];
  
  try {
    const stats = await RateLimit.aggregate([
      { $match: { date: today } },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          usersAtLimit: {
            $sum: { $cond: [{ $gte: ['$count', DAILY_LIMIT] }, 1, 0] }
          },
          totalGenerationsToday: { $sum: '$count' },
        }
      }
    ]);
    
    return stats[0] || {
      totalUsers: 0,
      usersAtLimit: 0,
      totalGenerationsToday: 0,
    };
    
  } catch (error) {
    console.error('Rate limit stats error:', error);
    return {
      totalUsers: 0,
      usersAtLimit: 0,
      totalGenerationsToday: 0,
    };
  }
}
