import mongoose from 'mongoose';

export interface IComment {
  _id: string;
  userId: string;
  imageId: string;
  content: string;
  likes: string[];
  replies: string[];
  parentComment?: string;
  createdAt: Date;
  updatedAt: Date;
}

const CommentSchema = new mongoose.Schema<IComment>({
  userId: {
    type: String,
    required: true,
    index: true,
    ref: 'User',
  },
  imageId: {
    type: String,
    required: true,
    index: true,
    ref: 'Image',
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  likes: [{
    type: String,
    ref: 'User',
  }],
  replies: [{
    type: String,
    ref: 'Comment',
  }],
  parentComment: {
    type: String,
    ref: 'Comment',
  },
}, {
  timestamps: true,
});

// Create indexes
CommentSchema.index({ imageId: 1, createdAt: -1 });
CommentSchema.index({ userId: 1, createdAt: -1 });
CommentSchema.index({ parentComment: 1 });

export default mongoose.models.Comment || mongoose.model<IComment>('Comment', CommentSchema);
