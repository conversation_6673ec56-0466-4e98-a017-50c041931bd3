import mongoose from 'mongoose';

const CustomStyleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  basePrompt: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500,
  },
  createdBy: {
    type: String,
    required: true,
    ref: 'User',
  },
  creatorName: {
    type: String,
    required: true,
  },
  examples: [{
    imageUrl: String,
    prompt: String,
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  category: {
    type: String,
    enum: ['artistic', 'photographic', 'abstract', 'fantasy', 'sci-fi', 'vintage', 'modern', 'experimental'],
    default: 'artistic',
  },
  isPublic: {
    type: Boolean,
    default: false,
  },
  isFeatured: {
    type: Boolean,
    default: false,
  },
  isApproved: {
    type: Boolean,
    default: false,
  },
  downloads: {
    type: Number,
    default: 0,
  },
  likes: [{
    type: String,
    ref: 'User',
  }],
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5,
  },
  ratingCount: {
    type: Number,
    default: 0,
  },
  usageCount: {
    type: Number,
    default: 0,
  },
  moderationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'pending',
  },
  moderationNotes: {
    type: String,
    trim: true,
  },
}, {
  timestamps: true,
});

// Indexes for performance
CustomStyleSchema.index({ createdBy: 1 });
CustomStyleSchema.index({ isPublic: 1, isApproved: 1 });
CustomStyleSchema.index({ isFeatured: 1 });
CustomStyleSchema.index({ category: 1 });
CustomStyleSchema.index({ rating: -1 });
CustomStyleSchema.index({ downloads: -1 });
CustomStyleSchema.index({ createdAt: -1 });

// Virtual for like count
CustomStyleSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Ensure virtual fields are serialized
CustomStyleSchema.set('toJSON', { virtuals: true });

export default mongoose.models.CustomStyle || mongoose.model('CustomStyle', CustomStyleSchema);
