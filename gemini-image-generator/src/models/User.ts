import mongoose from 'mongoose';

export interface IUser {
  _id: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  bio?: string;
  username: string;
  followers: string[];
  following: string[];
  totalGenerations: number;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new mongoose.Schema<IUser>({
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  email: {
    type: String,
    required: true,
    index: true,
  },
  displayName: {
    type: String,
    required: true,
  },
  photoURL: {
    type: String,
  },
  bio: {
    type: String,
    maxlength: 500,
  },
  username: {
    type: String,
    unique: true,
    index: true,
    minlength: 3,
    maxlength: 30,
  },
  followers: [{
    type: String,
    ref: 'User',
  }],
  following: [{
    type: String,
    ref: 'User',
  }],
  totalGenerations: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Create indexes
UserSchema.index({ username: 1 });
UserSchema.index({ firebaseUid: 1 });
UserSchema.index({ email: 1 });

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
