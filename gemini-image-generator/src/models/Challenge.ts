import mongoose from 'mongoose';

const ChallengeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500,
  },
  theme: {
    type: String,
    required: true,
    trim: true,
  },
  hashtag: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    unique: true,
  },
  type: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'special'],
    default: 'weekly',
  },
  category: {
    type: String,
    enum: ['style', 'theme', 'technique', 'collaboration', 'beginner', 'advanced'],
    default: 'theme',
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate',
  },
  promptSuggestions: [{
    type: String,
    trim: true,
  }],
  rules: [{
    type: String,
    trim: true,
  }],
  prizes: [{
    type: String,
    trim: true,
  }],
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
  votingEndDate: {
    type: Date,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isFeatured: {
    type: Boolean,
    default: false,
  },
  maxSubmissions: {
    type: Number,
    default: 1, // How many submissions per user
  },
  submissionCount: {
    type: Number,
    default: 0,
  },
  participantCount: {
    type: Number,
    default: 0,
  },
  createdBy: {
    type: String,
    required: true,
    ref: 'User',
  },
  moderators: [{
    type: String,
    ref: 'User',
  }],
  winners: [{
    userId: {
      type: String,
      ref: 'User',
    },
    imageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Image',
    },
    position: {
      type: Number, // 1st, 2nd, 3rd place
    },
    votes: {
      type: Number,
      default: 0,
    },
  }],
  status: {
    type: String,
    enum: ['upcoming', 'active', 'voting', 'completed', 'cancelled'],
    default: 'upcoming',
  },
  bannerImage: {
    type: String,
    trim: true,
  },
  socialLinks: {
    twitter: String,
    instagram: String,
    discord: String,
  },
}, {
  timestamps: true,
});

// Indexes for performance
ChallengeSchema.index({ status: 1, startDate: 1 });
ChallengeSchema.index({ endDate: 1 });
ChallengeSchema.index({ isFeatured: 1 });
ChallengeSchema.index({ type: 1 });
ChallengeSchema.index({ category: 1 });
ChallengeSchema.index({ hashtag: 1 });

// Virtual for days remaining
ChallengeSchema.virtual('daysRemaining').get(function() {
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

// Virtual for voting days remaining
ChallengeSchema.virtual('votingDaysRemaining').get(function() {
  const now = new Date();
  const votingEnd = new Date(this.votingEndDate);
  const diffTime = votingEnd.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

// Method to update status based on dates
ChallengeSchema.methods.updateStatus = function() {
  const now = new Date();
  const start = new Date(this.startDate);
  const end = new Date(this.endDate);
  const votingEnd = new Date(this.votingEndDate);

  if (now < start) {
    this.status = 'upcoming';
  } else if (now >= start && now < end) {
    this.status = 'active';
  } else if (now >= end && now < votingEnd) {
    this.status = 'voting';
  } else if (now >= votingEnd) {
    this.status = 'completed';
  }

  return this.status;
};

// Ensure virtual fields are serialized
ChallengeSchema.set('toJSON', { virtuals: true });

export default mongoose.models.Challenge || mongoose.model('Challenge', ChallengeSchema);
