import mongoose from 'mongoose';

export interface IRateLimit {
  userId: string;
  date: string; // YYYY-MM-DD format
  count: number;
  createdAt: Date;
  updatedAt: Date;
}

const RateLimitSchema = new mongoose.Schema<IRateLimit>({
  userId: {
    type: String,
    required: true,
    index: true,
  },
  date: {
    type: String,
    required: true,
    index: true,
  },
  count: {
    type: Number,
    required: true,
    default: 0,
  },
}, {
  timestamps: true,
});

// Create compound index for efficient queries
RateLimitSchema.index({ userId: 1, date: 1 }, { unique: true });

// TTL index to automatically delete old records after 30 days
RateLimitSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

const RateLimit = mongoose.models.RateLimit || mongoose.model<IRateLimit>('RateLimit', RateLimitSchema);

export default RateLimit;
