import mongoose from 'mongoose';

export interface ICollection {
  _id: string;
  userId: string;
  name: string;
  description?: string;
  images: string[];
  isPublic: boolean;
  coverImage?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

const CollectionSchema = new mongoose.Schema<ICollection>({
  userId: {
    type: String,
    required: true,
    index: true,
    ref: 'User',
  },
  name: {
    type: String,
    required: true,
    maxlength: 100,
  },
  description: {
    type: String,
    maxlength: 500,
  },
  images: [{
    type: String,
    ref: 'Image',
  }],
  isPublic: {
    type: Boolean,
    default: false,
  },
  coverImage: {
    type: String,
    ref: 'Image',
  },
  tags: [{
    type: String,
    maxlength: 50,
  }],
}, {
  timestamps: true,
});

// Create indexes
CollectionSchema.index({ userId: 1, createdAt: -1 });
CollectionSchema.index({ isPublic: 1, createdAt: -1 });
CollectionSchema.index({ tags: 1 });

export default mongoose.models.Collection || mongoose.model<ICollection>('Collection', CollectionSchema);
