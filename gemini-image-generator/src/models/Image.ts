import mongoose from 'mongoose';

export interface IImage {
  _id: string;
  userId: string;
  prompt: string;
  imageUrl: string;
  imageData: string; // base64 encoded image
  style?: string;
  mood?: string;
  colorPalette?: string[];
  isPublic: boolean;
  likes: string[];
  comments: string[];
  collections: string[];
  tags: string[];
  generationTime: number; // in milliseconds
  metadata: {
    model: string;
    timestamp: Date;
    settings?: Record<string, unknown>;
  };
  createdAt: Date;
  updatedAt: Date;
}

const ImageSchema = new mongoose.Schema<IImage>({
  userId: {
    type: String,
    required: true,
    index: true,
    ref: 'User',
  },
  prompt: {
    type: String,
    required: true,
    maxlength: 2000,
  },
  imageUrl: {
    type: String,
    required: true,
  },
  imageData: {
    type: String,
    required: true,
  },
  style: {
    type: String,
    enum: ['realistic', 'artistic', 'cartoon', 'abstract', 'vintage', 'modern', 'fantasy', 'sci-fi'],
    default: null,
  },
  mood: {
    type: String,
    enum: ['happy', 'sad', 'energetic', 'calm', 'mysterious', 'dramatic', 'peaceful', 'intense'],
    default: null,
  },
  colorPalette: [{
    type: String,
  }],
  isPublic: {
    type: Boolean,
    default: false,
  },
  likes: [{
    type: String,
    ref: 'User',
  }],
  comments: [{
    type: String,
    ref: 'Comment',
  }],
  collections: [{
    type: String,
    ref: 'Collection',
  }],
  tags: [{
    type: String,
    maxlength: 50,
  }],
  generationTime: {
    type: Number,
    default: 0,
  },
  metadata: {
    model: {
      type: String,
      default: 'gemini-2.0-flash-preview-image-generation',
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    settings: {
      type: mongoose.Schema.Types.Mixed,
    },
  },
}, {
  timestamps: true,
});

// Create indexes
ImageSchema.index({ userId: 1, createdAt: -1 });
ImageSchema.index({ isPublic: 1, createdAt: -1 });
ImageSchema.index({ tags: 1 });
ImageSchema.index({ style: 1 });
ImageSchema.index({ mood: 1 });
ImageSchema.index({ 'metadata.timestamp': -1 });
ImageSchema.index({ likes: 1 }); // Index for likes array for faster queries

export default mongoose.models.Image || mongoose.model<IImage>('Image', ImageSchema);
