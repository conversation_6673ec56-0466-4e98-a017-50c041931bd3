import mongoose from 'mongoose';

const ChallengeSubmissionSchema = new mongoose.Schema({
  challengeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Challenge',
    required: true,
  },
  imageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Image',
    required: true,
  },
  userId: {
    type: String,
    required: true,
    ref: 'User',
  },
  userName: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  technique: {
    type: String,
    trim: true,
    maxlength: 200,
  },
  votes: [{
    userId: {
      type: String,
      ref: 'User',
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  voteCount: {
    type: Number,
    default: 0,
  },
  isWinner: {
    type: Boolean,
    default: false,
  },
  winnerPosition: {
    type: Number, // 1, 2, 3 for 1st, 2nd, 3rd place
  },
  isDisqualified: {
    type: Boolean,
    default: false,
  },
  disqualificationReason: {
    type: String,
    trim: true,
  },
  moderationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'pending',
  },
  moderationNotes: {
    type: String,
    trim: true,
  },
  submissionOrder: {
    type: Number, // Order of submission for tiebreaking
  },
}, {
  timestamps: true,
});

// Indexes for performance
ChallengeSubmissionSchema.index({ challengeId: 1, userId: 1 }, { unique: true });
ChallengeSubmissionSchema.index({ challengeId: 1, voteCount: -1 });
ChallengeSubmissionSchema.index({ userId: 1 });
ChallengeSubmissionSchema.index({ isWinner: 1 });
ChallengeSubmissionSchema.index({ moderationStatus: 1 });

// Virtual for vote count (computed from votes array)
ChallengeSubmissionSchema.virtual('computedVoteCount').get(function() {
  return this.votes ? this.votes.length : 0;
});

// Pre-save middleware to update vote count
ChallengeSubmissionSchema.pre('save', function(next) {
  if (this.votes) {
    this.voteCount = this.votes.length;
  }
  next();
});

// Method to add vote
ChallengeSubmissionSchema.methods.addVote = function(userId: string) {
  if (!this.votes.some((vote: any) => vote.userId === userId)) {
    this.votes.push({ userId });
    this.voteCount = this.votes.length;
    return true;
  }
  return false; // Already voted
};

// Method to remove vote
ChallengeSubmissionSchema.methods.removeVote = function(userId: string) {
  const initialLength = this.votes.length;
  this.votes = this.votes.filter((vote: any) => vote.userId !== userId);
  this.voteCount = this.votes.length;
  return this.votes.length < initialLength; // Returns true if vote was removed
};

// Method to check if user has voted
ChallengeSubmissionSchema.methods.hasUserVoted = function(userId: string) {
  return this.votes.some((vote: any) => vote.userId === userId);
};

// Ensure virtual fields are serialized
ChallengeSubmissionSchema.set('toJSON', { virtuals: true });

export default mongoose.models.ChallengeSubmission || mongoose.model('ChallengeSubmission', ChallengeSubmissionSchema);
