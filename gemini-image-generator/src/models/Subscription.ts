import mongoose from 'mongoose';

const SubscriptionSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true,
    ref: 'User',
  },
  tier: {
    type: String,
    enum: ['free', 'creator', 'pro', 'enterprise'],
    default: 'free',
  },
  status: {
    type: String,
    enum: ['active', 'cancelled', 'expired', 'past_due', 'trialing'],
    default: 'active',
  },
  stripeCustomerId: {
    type: String,
    sparse: true,
  },
  stripeSubscriptionId: {
    type: String,
    sparse: true,
  },
  stripePriceId: {
    type: String,
  },
  currentPeriodStart: {
    type: Date,
  },
  currentPeriodEnd: {
    type: Date,
  },
  cancelAtPeriodEnd: {
    type: Boolean,
    default: false,
  },
  trialStart: {
    type: Date,
  },
  trialEnd: {
    type: Date,
  },
  features: {
    dailyImageLimit: {
      type: Number,
      default: 5,
    },
    hdGeneration: {
      type: Boolean,
      default: false,
    },
    noWatermark: {
      type: Boolean,
      default: false,
    },
    priorityQueue: {
      type: Boolean,
      default: false,
    },
    customStyles: {
      type: Boolean,
      default: false,
    },
    apiAccess: {
      type: Boolean,
      default: false,
    },
    batchGeneration: {
      type: Boolean,
      default: false,
    },
    commercialLicense: {
      type: Boolean,
      default: false,
    },
    upscaling: {
      type: Boolean,
      default: false,
    },
    prioritySupport: {
      type: Boolean,
      default: false,
    },
  },
  usage: {
    imagesThisMonth: {
      type: Number,
      default: 0,
    },
    apiCallsThisMonth: {
      type: Number,
      default: 0,
    },
    lastResetDate: {
      type: Date,
      default: Date.now,
    },
  },
  billing: {
    amount: {
      type: Number,
      default: 0,
    },
    currency: {
      type: String,
      default: 'usd',
    },
    interval: {
      type: String,
      enum: ['month', 'year'],
      default: 'month',
    },
  },
  metadata: {
    source: String,
    campaign: String,
    referrer: String,
  },
}, {
  timestamps: true,
});

// Indexes for performance
SubscriptionSchema.index({ userId: 1 }, { unique: true });
SubscriptionSchema.index({ stripeCustomerId: 1 });
SubscriptionSchema.index({ stripeSubscriptionId: 1 });
SubscriptionSchema.index({ status: 1 });
SubscriptionSchema.index({ tier: 1 });
SubscriptionSchema.index({ currentPeriodEnd: 1 });

// Method to check if subscription is active
SubscriptionSchema.methods.isActive = function() {
  return this.status === 'active' || this.status === 'trialing';
};

// Method to check if feature is available
SubscriptionSchema.methods.hasFeature = function(feature: string) {
  return this.features[feature] === true;
};

// Method to get daily image limit
SubscriptionSchema.methods.getDailyImageLimit = function() {
  return this.features.dailyImageLimit || 5;
};

// Method to reset monthly usage
SubscriptionSchema.methods.resetMonthlyUsage = function() {
  const now = new Date();
  const lastReset = new Date(this.usage.lastResetDate);
  
  // Reset if it's a new month
  if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
    this.usage.imagesThisMonth = 0;
    this.usage.apiCallsThisMonth = 0;
    this.usage.lastResetDate = now;
    return true;
  }
  return false;
};

// Static method to get tier features
SubscriptionSchema.statics.getTierFeatures = function(tier: string) {
  const tierFeatures = {
    free: {
      dailyImageLimit: 5,
      hdGeneration: false,
      noWatermark: false,
      priorityQueue: false,
      customStyles: false,
      apiAccess: false,
      batchGeneration: false,
      commercialLicense: false,
      upscaling: false,
      prioritySupport: false,
    },
    creator: {
      dailyImageLimit: 50,
      hdGeneration: true,
      noWatermark: true,
      priorityQueue: true,
      customStyles: true,
      apiAccess: false,
      batchGeneration: true,
      commercialLicense: false,
      upscaling: true,
      prioritySupport: false,
    },
    pro: {
      dailyImageLimit: 200,
      hdGeneration: true,
      noWatermark: true,
      priorityQueue: true,
      customStyles: true,
      apiAccess: true,
      batchGeneration: true,
      commercialLicense: true,
      upscaling: true,
      prioritySupport: true,
    },
    enterprise: {
      dailyImageLimit: 1000,
      hdGeneration: true,
      noWatermark: true,
      priorityQueue: true,
      customStyles: true,
      apiAccess: true,
      batchGeneration: true,
      commercialLicense: true,
      upscaling: true,
      prioritySupport: true,
    },
  };

  return tierFeatures[tier as keyof typeof tierFeatures] || tierFeatures.free;
};

export default mongoose.models.Subscription || mongoose.model('Subscription', SubscriptionSchema);
