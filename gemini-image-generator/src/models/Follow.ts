import mongoose from 'mongoose';

const FollowSchema = new mongoose.Schema({
  followerId: {
    type: String,
    required: true,
    ref: 'User',
  },
  followingId: {
    type: String,
    required: true,
    ref: 'User',
  },
  followerName: {
    type: String,
    required: true,
  },
  followingName: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

// Compound index to ensure unique follows and fast lookups
FollowSchema.index({ followerId: 1, followingId: 1 }, { unique: true });
FollowSchema.index({ followerId: 1 });
FollowSchema.index({ followingId: 1 });
FollowSchema.index({ createdAt: -1 });

// Prevent self-following
FollowSchema.pre('save', function(next) {
  if (this.followerId === this.followingId) {
    next(new Error('Cannot follow yourself'));
  } else {
    next();
  }
});

export default mongoose.models.Follow || mongoose.model('Follow', FollowSchema);
