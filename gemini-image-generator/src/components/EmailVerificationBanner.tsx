'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AlertCircle, Mail, X, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';

export default function EmailVerificationBanner() {
  const { user, sendVerificationEmail, checkEmailVerified } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    // Show banner if user is logged in but email is not verified
    if (user && !user.emailVerified) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [user]);

  const handleResendVerification = async () => {
    setLoading(true);
    try {
      await sendVerificationEmail();
      toast.success('Verification email sent! Please check your inbox.');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send verification email';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    setChecking(true);
    try {
      const isVerified = await checkEmailVerified();
      if (isVerified) {
        toast.success('Email verified successfully!');
        setIsVisible(false);
      } else {
        toast.error('Email not verified yet. Please check your email and click the verification link.');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check verification status';
      toast.error(errorMessage);
    } finally {
      setChecking(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  if (!isVisible || !user || user.emailVerified) {
    return null;
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-3 sm:p-4 mb-4 mx-2 sm:mx-0 rounded-r-lg">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400" />
        </div>
        <div className="ml-2 sm:ml-3 flex-1 min-w-0">
          <h3 className="text-xs sm:text-sm font-medium text-yellow-800 dark:text-yellow-200">
            Email Verification Required
          </h3>
          <div className="mt-1 sm:mt-2 text-xs sm:text-sm text-yellow-700 dark:text-yellow-300">
            <p>
              Please verify your email address to access all features.
              We sent a verification link to <strong className="break-all">{user.email}</strong>.
            </p>
          </div>
          <div className="mt-3 sm:mt-4 flex flex-col sm:flex-row gap-2">
            <button
              onClick={handleCheckVerification}
              disabled={checking}
              className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium text-yellow-800 dark:text-yellow-200 bg-yellow-100 dark:bg-yellow-800/30 rounded-md hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors disabled:opacity-50 flex-1 sm:flex-none"
            >
              {checking ? (
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-yellow-800"></div>
              ) : (
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
              )}
              <span className="hidden sm:inline">I've Verified My Email</span>
              <span className="sm:hidden">Verified</span>
            </button>
            <button
              onClick={handleResendVerification}
              disabled={loading}
              className="inline-flex items-center justify-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium text-yellow-800 dark:text-yellow-200 bg-yellow-100 dark:bg-yellow-800/30 rounded-md hover:bg-yellow-200 dark:hover:bg-yellow-800/50 transition-colors disabled:opacity-50 flex-1 sm:flex-none"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-yellow-800"></div>
              ) : (
                <Mail className="h-3 w-3 sm:h-4 sm:w-4" />
              )}
              <span className="hidden sm:inline">Resend Email</span>
              <span className="sm:hidden">Resend</span>
            </button>
          </div>
        </div>
        <div className="ml-2 sm:ml-auto sm:pl-3">
          <div className="-mx-1 -my-1 sm:-mx-1.5 sm:-my-1.5">
            <button
              onClick={handleDismiss}
              className="inline-flex rounded-md p-1 sm:p-1.5 text-yellow-500 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 focus:outline-none focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50"
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
