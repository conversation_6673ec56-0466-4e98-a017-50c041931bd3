'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Sparkles, Download, Heart, Share2, Wand2, MessageCircle, Info, Palette } from 'lucide-react';
import toast from 'react-hot-toast';
import CommentSection from './CommentSection';
import ImageDetailsModal from './ImageDetailsModal';
import StyleBrowser from './StyleBrowser';

interface GeneratedImage {
  _id: string;
  imageUrl: string;
  prompt: string;
  style?: string;
  mood?: string;
  colorPalette?: string[];
  generationTime: number;
  createdAt: string;
  likes?: string[];
}

export default function ImageGenerator() {
  const { user } = useAuth();
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [style, setStyle] = useState('');
  const [mood, setMood] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [showComments, setShowComments] = useState<string | null>(null);
  const [showImageDetails, setShowImageDetails] = useState<GeneratedImage | null>(null);
  const [showStyleBrowser, setShowStyleBrowser] = useState(false);
  const [rateLimitInfo, setRateLimitInfo] = useState<{
    current: number;
    remaining: number;
    limit: number;
    resetTime: Date;
  } | null>(null);

  useEffect(() => {
    if (user) {
      loadUserImages();
      loadRateLimitInfo();
    }
  }, [user]);

  const loadRateLimitInfo = async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/rate-limit/${user.uid}`);
      if (response.ok) {
        const data = await response.json();
        setRateLimitInfo({
          current: data.current,
          remaining: data.remaining,
          limit: data.limit,
          resetTime: new Date(data.resetTime),
        });
      }
    } catch (error) {
      console.error('Error loading rate limit info:', error);
    }
  };

  const loadUserImages = async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/images/user?userId=${user.uid}`);

      if (response.ok) {
        const images = await response.json();
        setGeneratedImages(images);
      }
    } catch (error) {
      console.error('Error loading user images:', error);
    }
  };

  const generateImage = async () => {
    if (!user) {
      setError('Please sign in to generate images.');
      return;
    }

    if (!prompt.trim()) {
      setError('Please enter a prompt to generate an image.');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
        body: JSON.stringify({
          prompt,
          style: style || null,
          mood: mood || null,
          isPublic,
          userId: user.uid,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle rate limit specifically
        if (response.status === 429 && data.rateLimitExceeded) {
          toast.error(data.error);
          loadRateLimitInfo(); // Refresh rate limit info
          return;
        }
        throw new Error(data.error || 'Failed to generate image');
      }

      if (data.success && data.image) {
        setGeneratedImages(prev => [data.image, ...prev]);
        setPrompt('');
        toast.success('Image generated successfully!');
        loadRateLimitInfo(); // Refresh rate limit info after successful generation
      }
    } catch (err: unknown) {
      console.error('Error generating image:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate image. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generateImage();
    }
  };

  const downloadImage = (imageUrl: string, prompt: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `gemini-generated-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const shareImage = async (_imageUrl: string, prompt: string, imageId: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Generated Image',
          text: `Check out this AI-generated image: "${prompt}"`,
          url: window.location.origin + '/image/' + imageId,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      const shareUrl = window.location.origin + '/image/' + imageId;
      navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard!');
    }
  };

  const handleStyleSelect = (customStyle: any) => {
    // Apply the custom style to the current prompt
    const enhancedPrompt = `${prompt} ${customStyle.basePrompt}`.trim();
    setPrompt(enhancedPrompt);
    setStyle(customStyle.category);
    setShowStyleBrowser(false);
    toast.success(`Applied "${customStyle.name}" style preset!`);
  };

  const likeImage = async (imageId: string) => {
    if (!user) {
      toast.error('Please sign in to like images');
      return;
    }

    // Prevent multiple rapid clicks
    const currentImage = generatedImages.find(img => img._id === imageId);
    if (!currentImage) return;

    try {
      const response = await fetch(`/api/images/${imageId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.uid }),
      });

      if (response.ok) {
        const data = await response.json();
        setGeneratedImages(prev =>
          prev.map(img =>
            img._id === imageId
              ? {
                  ...img,
                  likes: data.isLiked
                    ? [...(img.likes || []).filter(id => id !== user.uid), user.uid] // Ensure uniqueness
                    : (img.likes || []).filter(id => id !== user.uid)
                }
              : img
          )
        );
        toast.success(data.isLiked ? 'Image liked!' : 'Image unliked!');
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to like image');
      }
    } catch (error) {
      console.error('Error liking image:', error);
      toast.error('Failed to like image');
    }
  };

  if (!user) {
    return (
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
        <Sparkles className="w-16 h-16 text-indigo-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
          Sign In to Generate Images
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Create an account or sign in to start generating amazing AI images with Gemini 2.0 Flash.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Input Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <div className="flex items-center gap-3 mb-6">
          <Wand2 className="w-6 h-6 text-indigo-600" />
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
            Generate Image
          </h3>
        </div>

        {/* Style and Mood Selectors */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Style
            </label>
            <select
              value={style}
              onChange={(e) => setStyle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="">Any Style</option>
              <option value="realistic">Realistic</option>
              <option value="artistic">Artistic</option>
              <option value="cartoon">Cartoon</option>
              <option value="abstract">Abstract</option>
              <option value="vintage">Vintage</option>
              <option value="modern">Modern</option>
              <option value="fantasy">Fantasy</option>
              <option value="sci-fi">Sci-Fi</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Mood
            </label>
            <select
              value={mood}
              onChange={(e) => setMood(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="">Any Mood</option>
              <option value="happy">Happy</option>
              <option value="sad">Sad</option>
              <option value="energetic">Energetic</option>
              <option value="calm">Calm</option>
              <option value="mysterious">Mysterious</option>
              <option value="dramatic">Dramatic</option>
              <option value="peaceful">Peaceful</option>
              <option value="intense">Intense</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Community Styles
            </label>
            <button
              type="button"
              onClick={() => setShowStyleBrowser(true)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center justify-center gap-2 text-gray-700 dark:text-gray-300"
            >
              <Palette className="w-4 h-4" />
              Browse Styles
            </button>
          </div>

          <div className="flex items-end">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Make Public</span>
            </label>
          </div>
        </div>
        
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="Describe the image you want to generate... (e.g., 'A futuristic city with flying cars at sunset')"
          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none dark:bg-gray-700 dark:text-white"
          rows={3}
        />

        {/* Example Prompts */}
        <div className="mt-4">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Try these example prompts:
          </p>
          <div className="flex flex-wrap gap-2">
            {[
              "A majestic dragon flying over a medieval castle at sunset",
              "A cyberpunk street scene with neon lights and rain",
              "A peaceful zen garden with cherry blossoms",
              "A robot playing chess with a human",
              "A cozy coffee shop on a rainy day"
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setPrompt(example)}
                className="text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded transition-colors"
              >
                {example.length > 40 ? example.substring(0, 40) + '...' : example}
              </button>
            ))}
          </div>
        </div>

        {/* Rate Limit Info */}
        {user && rateLimitInfo && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <span className="font-medium">Daily Usage:</span> {rateLimitInfo.current}/{rateLimitInfo.limit} images
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-300">
                {rateLimitInfo.remaining > 0 ? (
                  `${rateLimitInfo.remaining} remaining`
                ) : (
                  `Resets at ${rateLimitInfo.resetTime.toLocaleTimeString()}`
                )}
              </div>
            </div>
            {rateLimitInfo.remaining === 0 && (
              <div className="mt-2 text-xs text-blue-700 dark:text-blue-300">
                You&apos;ve reached your daily limit. Come back tomorrow for more generations!
              </div>
            )}
          </div>
        )}

        <div className="flex justify-between items-center mt-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Press Enter to generate or Shift+Enter for new line
          </p>
          <button
            onClick={generateImage}
            disabled={isGenerating || !prompt.trim() || (rateLimitInfo?.remaining === 0)}
            className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-lg transition-colors flex items-center gap-2"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Generating...
              </>
            ) : rateLimitInfo?.remaining === 0 ? (
              <>
                Daily Limit Reached
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4" />
                Generate Image
              </>
            )}
          </button>
        </div>
        
        {error && (
          <div className="mt-4 p-3 bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 rounded">
            {error}
          </div>
        )}
      </div>

      {/* Generated Images */}
      {generatedImages.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-2xl font-semibold text-gray-800 dark:text-white">
            Generated Images
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {generatedImages.map((image) => (
              <div key={image._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden group">
                <div className="aspect-square relative">
                  <img
                    src={image.imageUrl}
                    alt={image.prompt}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-2">
                      <button
                        onClick={() => likeImage(image._id)}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all ${
                          user && image.likes?.includes(user.uid) ? 'text-red-500' : 'text-gray-700'
                        }`}
                        title="Like"
                      >
                        <Heart className={`w-5 h-5 ${user && image.likes?.includes(user.uid) ? 'fill-current' : ''}`} />
                      </button>
                      <button
                        onClick={() => setShowComments(image._id)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Comments"
                      >
                        <MessageCircle className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => setShowImageDetails(image)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="View Details & Script"
                      >
                        <Info className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => downloadImage(image.imageUrl, image.prompt)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Download"
                      >
                        <Download className="w-5 h-5 text-gray-700" />
                      </button>
                      <button
                        onClick={() => shareImage(image.imageUrl, image.prompt, image._id)}
                        className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                        title="Share"
                      >
                        <Share2 className="w-5 h-5 text-gray-700" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                    <strong>Prompt:</strong> {image.prompt}
                  </p>

                  {(image.style || image.mood) && (
                    <div className="flex gap-2 mb-2">
                      {image.style && (
                        <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 text-xs rounded-full">
                          {image.style}
                        </span>
                      )}
                      {image.mood && (
                        <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full">
                          {image.mood}
                        </span>
                      )}
                    </div>
                  )}

                  <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                    <span>Generated: {new Date(image.createdAt).toLocaleDateString()}</span>
                    <div className="flex items-center gap-2">
                      <Heart className="w-3 h-3" />
                      <span>{image.likes?.length || 0}</span>
                      <span className="ml-2">{image.generationTime}ms</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Comment Modal */}
      {showComments && (
        <CommentSection
          imageId={showComments}
          isOpen={!!showComments}
          onClose={() => setShowComments(null)}
        />
      )}

      {/* Image Details Modal */}
      {showImageDetails && (
        <ImageDetailsModal
          image={showImageDetails}
          isOpen={!!showImageDetails}
          onClose={() => setShowImageDetails(null)}
          onLike={likeImage}
          currentUserId={user?.uid}
        />
      )}

      {/* Style Browser Modal */}
      {showStyleBrowser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                Community Style Presets
              </h2>
              <button
                onClick={() => setShowStyleBrowser(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <StyleBrowser
                onStyleSelect={handleStyleSelect}
                showCreateButton={false}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
