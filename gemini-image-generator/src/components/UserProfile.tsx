'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Users, Heart, Image as ImageIcon, Calendar, UserPlus, UserMinus } from 'lucide-react';
import toast from 'react-hot-toast';

interface UserProfileProps {
  userId: string;
  displayName?: string;
  email?: string;
  photoURL?: string;
  bio?: string;
  createdAt?: string;
}

interface FollowStats {
  followerCount: number;
  followingCount: number;
  isFollowing: boolean;
}

interface UserStats {
  totalImages: number;
  publicImages: number;
  totalLikes: number;
}

export default function UserProfile({
  userId,
  displayName,
  email,
  photoURL,
  bio,
  createdAt
}: UserProfileProps) {
  const { user } = useAuth();
  const [followStats, setFollowStats] = useState<FollowStats>({
    followerCount: 0,
    followingCount: 0,
    isFollowing: false,
  });
  const [userStats, setUserStats] = useState<UserStats>({
    totalImages: 0,
    publicImages: 0,
    totalLikes: 0,
  });
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(true);

  const isOwnProfile = user?.uid === userId;

  useEffect(() => {
    loadUserData();
  }, [userId, user]);

  const loadUserData = async () => {
    try {
      setLoading(true);

      // Load follow stats
      const followResponse = await fetch(
        `/api/users/${userId}/follow?currentUserId=${user?.uid || ''}`
      );
      const followData = await followResponse.json();
      
      if (followData.success) {
        setFollowStats(followData);
        setIsFollowing(followData.isFollowing);
      }

      // Load user stats
      const statsResponse = await fetch(`/api/users/${userId}/stats`);
      const statsData = await statsResponse.json();
      
      if (statsData.success) {
        setUserStats(statsData.stats);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowToggle = async () => {
    if (!user) {
      toast.error('Please sign in to follow users');
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}/follow`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ followerId: user.uid }),
      });

      const data = await response.json();
      
      if (data.success) {
        setIsFollowing(data.isFollowing);
        setFollowStats(prev => ({
          ...prev,
          followerCount: data.followerCount,
          isFollowing: data.isFollowing,
        }));
        toast.success(data.message);
      } else {
        toast.error(data.error || 'Failed to follow/unfollow user');
      }
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error('Failed to follow/unfollow user');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
            <div className="flex-1">
              <div className="h-6 bg-gray-300 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-2/3"></div>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="h-16 bg-gray-300 rounded"></div>
            <div className="h-16 bg-gray-300 rounded"></div>
            <div className="h-16 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      {/* Profile Header */}
      <div className="flex items-start gap-4 mb-6">
        <div className="flex-shrink-0">
          {photoURL ? (
            <img
              src={photoURL}
              alt={displayName || email || 'User'}
              className="w-16 h-16 rounded-full object-cover"
            />
          ) : (
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xl font-bold">
                {(displayName || email || 'U').charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-1">
            {displayName || email || 'Anonymous User'}
          </h2>
          
          {bio && (
            <p className="text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
              {bio}
            </p>
          )}

          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>Joined {formatDate(createdAt)}</span>
          </div>
        </div>

        {/* Follow Button */}
        {!isOwnProfile && user && (
          <button
            onClick={handleFollowToggle}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              isFollowing
                ? 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                : 'bg-indigo-600 hover:bg-indigo-700 text-white'
            }`}
          >
            {isFollowing ? (
              <>
                <UserMinus className="w-4 h-4" />
                Unfollow
              </>
            ) : (
              <>
                <UserPlus className="w-4 h-4" />
                Follow
              </>
            )}
          </button>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <ImageIcon className="w-4 h-4 text-gray-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {userStats.totalImages}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Images</span>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Heart className="w-4 h-4 text-gray-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {userStats.totalLikes}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Likes</span>
        </div>

        <div className="text-center">
          <button className="w-full hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg p-2 transition-colors">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-2xl font-bold text-gray-800 dark:text-white">
                {followStats.followerCount}
              </span>
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-300">Followers</span>
          </button>
        </div>

        <div className="text-center">
          <button className="w-full hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg p-2 transition-colors">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-2xl font-bold text-gray-800 dark:text-white">
                {followStats.followingCount}
              </span>
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-300">Following</span>
          </button>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <ImageIcon className="w-4 h-4 text-gray-500" />
            <span className="text-2xl font-bold text-gray-800 dark:text-white">
              {userStats.publicImages}
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-300">Public</span>
        </div>
      </div>
    </div>
  );
}
