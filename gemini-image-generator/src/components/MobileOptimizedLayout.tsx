'use client';

import { ReactNode } from 'react';

interface MobileOptimizedLayoutProps {
  children: ReactNode;
  className?: string;
}

export default function MobileOptimizedLayout({ children, className = '' }: MobileOptimizedLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        {children}
      </div>
    </div>
  );
}
