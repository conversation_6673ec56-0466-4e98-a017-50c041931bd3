'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Image, Folder, Sparkles, Heart, Trophy } from 'lucide-react';

export default function MobileBottomNav() {
  const pathname = usePathname();

  const navItems = [
    {
      href: '/',
      icon: Image,
      label: 'Generate',
      isActive: pathname === '/'
    },
    {
      href: '/gallery',
      icon: Folder,
      label: 'Gallery',
      isActive: pathname === '/gallery'
    },
    {
      href: '/feed',
      icon: Sparkles,
      label: 'Feed',
      isActive: pathname === '/feed'
    },
    {
      href: '/explore',
      icon: Heart,
      label: 'Explore',
      isActive: pathname === '/explore'
    },
    {
      href: '/challenges',
      icon: Trophy,
      label: 'Challenges',
      isActive: pathname === '/challenges'
    }
  ];

  return (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-40">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex flex-col items-center justify-center px-3 py-2 rounded-lg transition-colors ${
                item.isActive
                  ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20'
                  : 'text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400'
              }`}
            >
              <Icon className={`w-5 h-5 mb-1 ${item.isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} />
              <span className={`text-xs font-medium ${item.isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`}>
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
