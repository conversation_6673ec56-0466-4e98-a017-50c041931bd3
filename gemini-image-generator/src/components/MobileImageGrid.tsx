'use client';

import { useState } from 'react';
import { Heart, MessageCircle, Download, Share2, Info } from 'lucide-react';
import toast from 'react-hot-toast';

interface GeneratedImage {
  _id: string;
  imageUrl: string;
  prompt: string;
  style?: string;
  mood?: string;
  colorPalette?: string[];
  generationTime: number;
  createdAt: string;
  likes?: string[];
}

interface MobileImageGridProps {
  images: GeneratedImage[];
  onLike?: (imageId: string) => void;
  onComment?: (imageId: string) => void;
  onDetails?: (image: GeneratedImage) => void;
  currentUserId?: string;
}

export default function MobileImageGrid({ 
  images, 
  onLike, 
  onComment, 
  onDetails,
  currentUserId 
}: MobileImageGridProps) {
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());

  const handleImageLoad = (imageId: string) => {
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
  };

  const handleImageError = (imageId: string) => {
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
    toast.error('Failed to load image');
  };

  const downloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-art-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Image downloaded!');
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download image');
    }
  };

  const shareImage = async (imageUrl: string, prompt: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI Generated Art',
          text: prompt,
          url: imageUrl,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback to copying URL
      try {
        await navigator.clipboard.writeText(imageUrl);
        toast.success('Image URL copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy URL');
      }
    }
  };

  if (images.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🎨</div>
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          No images yet
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Generate your first AI image to get started!
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
      {images.map((image) => (
        <div
          key={image._id}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden"
        >
          {/* Image */}
          <div className="relative aspect-square">
            {loadingImages.has(image._id) && (
              <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center">
                <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <img
              src={image.imageUrl}
              alt={image.prompt}
              className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
              onLoad={() => handleImageLoad(image._id)}
              onError={() => handleImageError(image._id)}
              onClick={() => onDetails?.(image)}
            />
            
            {/* Overlay with quick actions */}
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-end">
              <div className="w-full p-2 bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100 transition-opacity">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center gap-2">
                    {onLike && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onLike(image._id);
                        }}
                        className="p-1.5 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                      >
                        <Heart 
                          className={`w-4 h-4 ${
                            image.likes?.includes(currentUserId || '') 
                              ? 'fill-red-500 text-red-500' 
                              : 'text-white'
                          }`} 
                        />
                      </button>
                    )}
                    {onComment && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onComment(image._id);
                        }}
                        className="p-1.5 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                      >
                        <MessageCircle className="w-4 h-4 text-white" />
                      </button>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadImage(image.imageUrl, image.prompt);
                      }}
                      className="p-1.5 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                    >
                      <Download className="w-4 h-4 text-white" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        shareImage(image.imageUrl, image.prompt);
                      }}
                      className="p-1.5 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                    >
                      <Share2 className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-3">
            <p className="text-sm text-gray-800 dark:text-white line-clamp-2 mb-2">
              {image.prompt}
            </p>
            
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-2">
                {image.style && (
                  <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                    {image.style}
                  </span>
                )}
                {image.mood && (
                  <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                    {image.mood}
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {image.likes && image.likes.length > 0 && (
                  <span className="flex items-center gap-1">
                    <Heart className="w-3 h-3" />
                    {image.likes.length}
                  </span>
                )}
                <button
                  onClick={() => onDetails?.(image)}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                >
                  <Info className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
