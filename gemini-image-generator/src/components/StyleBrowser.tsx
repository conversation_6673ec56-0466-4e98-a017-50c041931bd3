'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Heart, Star, Download, Eye, Filter, Plus } from 'lucide-react';
import toast from 'react-hot-toast';

interface CustomStyle {
  _id: string;
  name: string;
  description: string;
  basePrompt: string;
  createdBy: string;
  creatorName: string;
  examples: Array<{ imageUrl: string; prompt: string }>;
  tags: string[];
  category: string;
  isPublic: boolean;
  isFeatured: boolean;
  downloads: number;
  likes: string[];
  rating: number;
  ratingCount: number;
  usageCount: number;
  createdAt: string;
  likeCount: number;
}

interface StyleBrowserProps {
  onStyleSelect?: (style: CustomStyle) => void;
  showCreateButton?: boolean;
  userOnly?: boolean;
}

export default function StyleBrowser({ 
  onStyleSelect, 
  showCreateButton = true,
  userOnly = false 
}: StyleBrowserProps) {
  const { user } = useAuth();
  const [styles, setStyles] = useState<CustomStyle[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'artistic', label: 'Artistic' },
    { value: 'photographic', label: 'Photographic' },
    { value: 'abstract', label: 'Abstract' },
    { value: 'fantasy', label: 'Fantasy' },
    { value: 'sci-fi', label: 'Sci-Fi' },
    { value: 'vintage', label: 'Vintage' },
    { value: 'modern', label: 'Modern' },
    { value: 'experimental', label: 'Experimental' },
  ];

  const sortOptions = [
    { value: 'recent', label: 'Most Recent' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'rating', label: 'Highest Rated' },
  ];

  useEffect(() => {
    loadStyles();
  }, [selectedCategory, sortBy, showFeaturedOnly, userOnly, user]);

  const loadStyles = async () => {
    try {
      setLoading(true);
      
      let url = '/api/styles';
      if (userOnly && user) {
        url = `/api/users/${user.uid}/styles?includePrivate=true`;
      } else {
        const params = new URLSearchParams({
          category: selectedCategory,
          sort: sortBy,
          featured: showFeaturedOnly.toString(),
        });
        url += `?${params}`;
      }

      const response = await fetch(url);
      const data = await response.json();

      if (data.success) {
        setStyles(data.styles || []);
      } else {
        toast.error('Failed to load styles');
      }
    } catch (error) {
      console.error('Error loading styles:', error);
      toast.error('Failed to load styles');
    } finally {
      setLoading(false);
    }
  };

  const handleLikeStyle = async (styleId: string) => {
    if (!user) {
      toast.error('Please sign in to like styles');
      return;
    }

    try {
      const response = await fetch(`/api/styles/${styleId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.uid }),
      });

      const data = await response.json();
      if (data.success) {
        setStyles(styles.map(style => 
          style._id === styleId 
            ? { 
                ...style, 
                likes: data.liked 
                  ? [...style.likes, user.uid]
                  : style.likes.filter(id => id !== user.uid),
                likeCount: data.likeCount 
              }
            : style
        ));
      }
    } catch (error) {
      console.error('Error liking style:', error);
      toast.error('Failed to like style');
    }
  };

  const handleUseStyle = (style: CustomStyle) => {
    if (onStyleSelect) {
      onStyleSelect(style);
      toast.success(`Applied "${style.name}" style!`);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            {userOnly ? 'My Custom Styles' : 'Community Styles'}
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            {userOnly 
              ? 'Manage your custom style presets'
              : 'Discover and use community-created style presets'
            }
          </p>
        </div>
        
        {showCreateButton && (
          <button
            onClick={() => {/* TODO: Open create style modal */}}
            className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Style
          </button>
        )}
      </div>

      {/* Filters */}
      {!userOnly && (
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            >
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
          </div>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>

          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={showFeaturedOnly}
              onChange={(e) => setShowFeaturedOnly(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Featured only</span>
          </label>
        </div>
      )}

      {/* Styles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {styles.map((style) => (
          <div
            key={style._id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow"
          >
            {/* Style Header */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
                    {style.name}
                    {style.isFeatured && (
                      <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        Featured
                      </span>
                    )}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {style.description}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    by {style.creatorName}
                  </p>
                </div>
              </div>

              {/* Rating and Stats */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    {renderStars(style.rating)}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {style.rating.toFixed(1)} ({style.ratingCount})
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Download className="w-3 h-3" />
                    {style.downloads}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {style.usageCount}
                  </span>
                </div>
              </div>

              {/* Tags */}
              {style.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {style.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                  {style.tags.length > 3 && (
                    <span className="text-xs text-gray-500">+{style.tags.length - 3}</span>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between">
                <button
                  onClick={() => handleUseStyle(style)}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Use Style
                </button>
                
                <button
                  onClick={() => handleLikeStyle(style._id)}
                  className={`flex items-center gap-1 px-2 py-1 rounded text-sm transition-colors ${
                    user && style.likes.includes(user.uid)
                      ? 'text-red-600 bg-red-50 dark:bg-red-900/20'
                      : 'text-gray-600 dark:text-gray-300 hover:text-red-600'
                  }`}
                >
                  <Heart className={`w-4 h-4 ${
                    user && style.likes.includes(user.uid) ? 'fill-current' : ''
                  }`} />
                  {style.likeCount || 0}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {styles.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">
            {userOnly ? 'No custom styles created yet.' : 'No styles found.'}
          </p>
        </div>
      )}
    </div>
  );
}
