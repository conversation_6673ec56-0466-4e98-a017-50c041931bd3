'use client';

import { useState } from 'react';
import { Copy, Download, Heart, MessageCircle, X, Clock, Palette, Sparkles } from 'lucide-react';
import toast from 'react-hot-toast';
import CommentSection from './CommentSection';

interface ImageDetailsProps {
  image: {
    _id: string;
    imageUrl: string;
    prompt: string;
    style?: string;
    mood?: string;
    colorPalette?: string[];
    generationTime: number;
    createdAt: string;
    likes?: string[];
    user?: {
      displayName: string;
      photoURL?: string;
      username: string;
    };
  };
  isOpen: boolean;
  onClose: () => void;
  onLike?: (imageId: string) => void;
  currentUserId?: string;
}

export default function ImageDetailsModal({ 
  image, 
  isOpen, 
  onClose, 
  onLike, 
  currentUserId 
}: ImageDetailsProps) {
  const [showComments, setShowComments] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'script'>('details');

  if (!isOpen) return null;

  const generateScript = () => {
    const script = `// Gemini AI Flash Image Generation Script
// Generated on: ${new Date(image.createdAt).toLocaleString()}

import { GoogleGenAI, Modality } from '@google/genai';

async function generateImage() {
  // Initialize Gemini AI
  const ai = new GoogleGenAI({ apiKey: 'YOUR_API_KEY' });

  // Generation parameters
  const prompt = "${image.prompt}";
  const style = "${image.style || 'any'}";
  const mood = "${image.mood || 'any'}";
  
  try {
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash-preview-image-generation",
      contents: prompt,
      config: {
        responseModalities: [Modality.TEXT, Modality.IMAGE],
      },
    });

    // Extract image data
    for (const part of response.candidates[0].content.parts) {
      if (part.inlineData) {
        const imageData = part.inlineData.data;
        const imageUrl = \`data:image/png;base64,\${imageData}\`;
        
        console.log('Image generated successfully!');
        console.log('Generation time: ${image.generationTime}ms');
        
        return {
          imageUrl,
          prompt,
          style,
          mood,
          generationTime: ${image.generationTime}
        };
      }
    }
  } catch (error) {
    console.error('Error generating image:', error);
  }
}

// Usage
generateImage().then(result => {
  if (result) {
    console.log('Generated image:', result);
    // Use the image in your application
  }
});

/* 
 * Generation Settings Used:
 * - Model: gemini-2.0-flash-preview-image-generation
 * - Prompt: &quot;${image.prompt}&quot;
 * - Style: ${image.style || 'Not specified'}
 * - Mood: ${image.mood || 'Not specified'}
 * - Generation Time: ${image.generationTime}ms
 * - Created: ${new Date(image.createdAt).toLocaleString()}
 */`;

    return script;
  };

  const copyScript = () => {
    navigator.clipboard.writeText(generateScript());
    toast.success('Script copied to clipboard!');
  };

  const downloadScript = () => {
    const script = generateScript();
    const blob = new Blob([script], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `gemini-image-generation-${image._id}.js`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success('Script downloaded!');
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = image.imageUrl;
    link.download = `gemini-generated-${image.prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <Sparkles className="w-6 h-6 text-indigo-600" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                Image Details
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden flex">
            {/* Image Preview */}
            <div className="w-1/2 p-6">
              <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                <img
                  src={image.imageUrl}
                  alt={image.prompt}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Image Actions */}
              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => onLike?.(image._id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    currentUserId && image.likes?.includes(currentUserId)
                      ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <Heart className={`w-4 h-4 ${currentUserId && image.likes?.includes(currentUserId) ? 'fill-current' : ''}`} />
                  {image.likes?.length || 0}
                </button>
                
                <button
                  onClick={() => setShowComments(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <MessageCircle className="w-4 h-4" />
                  Comments
                </button>
                
                <button
                  onClick={downloadImage}
                  className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Download
                </button>
              </div>

              {/* Creator Info */}
              {image.user && (
                <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-800 dark:text-white mb-2">Created by</h4>
                  <div className="flex items-center gap-3">
                    {image.user.photoURL ? (
                      <img
                        src={image.user.photoURL}
                        alt={image.user.displayName}
                        className="w-8 h-8 rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {image.user.displayName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-gray-800 dark:text-white text-sm">
                        {image.user.displayName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        @{image.user.username}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Details Panel */}
            <div className="w-1/2 border-l border-gray-200 dark:border-gray-700 flex flex-col">
              {/* Tabs */}
              <div className="flex border-b border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === 'details'
                      ? 'text-indigo-600 border-b-2 border-indigo-600'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  }`}
                >
                  Details
                </button>
                <button
                  onClick={() => setActiveTab('script')}
                  className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === 'script'
                      ? 'text-indigo-600 border-b-2 border-indigo-600'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  }`}
                >
                  Generation Script
                </button>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-y-auto p-6">
                {activeTab === 'details' ? (
                  <div className="space-y-6">
                    {/* Prompt */}
                    <div>
                      <h4 className="font-medium text-gray-800 dark:text-white mb-2">Prompt</h4>
                      <p className="text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        {image.prompt}
                      </p>
                    </div>

                    {/* Generation Settings */}
                    <div>
                      <h4 className="font-medium text-gray-800 dark:text-white mb-3">Generation Settings</h4>
                      <div className="space-y-3">
                        {image.style && (
                          <div className="flex items-center gap-3">
                            <Palette className="w-4 h-4 text-indigo-600" />
                            <span className="text-sm text-gray-600 dark:text-gray-300">Style:</span>
                            <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 text-xs rounded-full">
                              {image.style}
                            </span>
                          </div>
                        )}
                        
                        {image.mood && (
                          <div className="flex items-center gap-3">
                            <Heart className="w-4 h-4 text-purple-600" />
                            <span className="text-sm text-gray-600 dark:text-gray-300">Mood:</span>
                            <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full">
                              {image.mood}
                            </span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-3">
                          <Clock className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">Generation Time:</span>
                          <span className="text-sm font-medium text-gray-800 dark:text-white">
                            {image.generationTime}ms
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <Sparkles className="w-4 h-4 text-yellow-600" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">Model:</span>
                          <span className="text-sm font-medium text-gray-800 dark:text-white">
                            Gemini 2.0 Flash
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <Clock className="w-4 h-4 text-gray-600" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">Created:</span>
                          <span className="text-sm font-medium text-gray-800 dark:text-white">
                            {new Date(image.createdAt).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Color Palette */}
                    {image.colorPalette && image.colorPalette.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-800 dark:text-white mb-3">Color Palette</h4>
                        <div className="flex gap-2">
                          {image.colorPalette.map((color, index) => (
                            <div
                              key={index}
                              className="w-8 h-8 rounded-full border-2 border-gray-200 dark:border-gray-600"
                              style={{ backgroundColor: color }}
                              title={color}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-800 dark:text-white">Generation Script</h4>
                      <div className="flex gap-2">
                        <button
                          onClick={copyScript}
                          className="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-600"
                        >
                          <Copy className="w-3 h-3" />
                          Copy
                        </button>
                        <button
                          onClick={downloadScript}
                          className="flex items-center gap-2 px-3 py-1 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700"
                        >
                          <Download className="w-3 h-3" />
                          Download
                        </button>
                      </div>
                    </div>
                    
                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                      <pre className="text-sm text-gray-100">
                        <code>{generateScript()}</code>
                      </pre>
                    </div>
                    
                    <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                      <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">How to use this script:</h5>
                      <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
                        <li>Install the Gemini AI SDK: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">npm install @google/genai</code></li>
                        <li>Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="underline">Google AI Studio</a></li>
                        <li>Replace &apos;YOUR_API_KEY&apos; with your actual API key</li>
                        <li>Run the script to generate a similar image</li>
                      </ol>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Comments Modal */}
      <CommentSection
        imageId={image._id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
      />
    </>
  );
}
