'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Calendar, Users, Trophy, Clock, Tag, Star } from 'lucide-react';
import toast from 'react-hot-toast';

interface Challenge {
  _id: string;
  title: string;
  description: string;
  theme: string;
  hashtag: string;
  type: string;
  category: string;
  difficulty: string;
  startDate: string;
  endDate: string;
  votingEndDate: string;
  status: string;
  submissionCount: number;
  participantCount: number;
  prizes: string[];
  daysRemaining: number;
  votingDaysRemaining: number;
  isFeatured: boolean;
}

interface ChallengeCardProps {
  challenge: Challenge;
  onJoinChallenge?: (challengeId: string) => void;
  showActions?: boolean;
}

export default function ChallengeCard({ 
  challenge, 
  onJoinChallenge,
  showActions = true 
}: ChallengeCardProps) {
  const { user } = useAuth();
  const [isJoining, setIsJoining] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'voting':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'advanced':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'expert':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const handleJoinChallenge = async () => {
    if (!user) {
      toast.error('Please sign in to join challenges');
      return;
    }

    if (challenge.status !== 'active') {
      toast.error('This challenge is not currently accepting submissions');
      return;
    }

    setIsJoining(true);
    try {
      if (onJoinChallenge) {
        onJoinChallenge(challenge._id);
      }
    } catch (error) {
      console.error('Error joining challenge:', error);
      toast.error('Failed to join challenge');
    } finally {
      setIsJoining(false);
    }
  };

  const getTimeRemaining = () => {
    if (challenge.status === 'active') {
      return `${challenge.daysRemaining} days left to submit`;
    } else if (challenge.status === 'voting') {
      return `${challenge.votingDaysRemaining} days left to vote`;
    } else if (challenge.status === 'upcoming') {
      const startDate = new Date(challenge.startDate);
      const now = new Date();
      const daysUntilStart = Math.ceil((startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return `Starts in ${daysUntilStart} days`;
    }
    return 'Completed';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                {challenge.title}
              </h3>
              {challenge.isFeatured && (
                <Star className="w-5 h-5 text-yellow-500 fill-current" />
              )}
            </div>
            
            <div className="flex flex-wrap gap-2 mb-3">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(challenge.status)}`}>
                {challenge.status.charAt(0).toUpperCase() + challenge.status.slice(1)}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(challenge.difficulty)}`}>
                {challenge.difficulty.charAt(0).toUpperCase() + challenge.difficulty.slice(1)}
              </span>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                {challenge.category}
              </span>
            </div>
          </div>
        </div>

        <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
          {challenge.description}
        </p>

        {/* Theme and Hashtag */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <Tag className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {challenge.theme}
            </span>
          </div>
          <div className="text-sm text-indigo-600 dark:text-indigo-400 font-mono">
            #{challenge.hashtag}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-lg font-bold text-gray-800 dark:text-white">
                {challenge.participantCount}
              </span>
            </div>
            <span className="text-xs text-gray-500">Participants</span>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Trophy className="w-4 h-4 text-gray-500" />
              <span className="text-lg font-bold text-gray-800 dark:text-white">
                {challenge.submissionCount}
              </span>
            </div>
            <span className="text-xs text-gray-500">Submissions</span>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-lg font-bold text-gray-800 dark:text-white">
                {challenge.status === 'active' ? challenge.daysRemaining : 
                 challenge.status === 'voting' ? challenge.votingDaysRemaining : '0'}
              </span>
            </div>
            <span className="text-xs text-gray-500">Days Left</span>
          </div>
        </div>

        {/* Time Remaining */}
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {getTimeRemaining()}
          </span>
        </div>

        {/* Prizes */}
        {challenge.prizes.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Prizes:
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {challenge.prizes.slice(0, 3).map((prize, index) => (
                <li key={index} className="flex items-center gap-2">
                  <Trophy className="w-3 h-3 text-yellow-500" />
                  {prize}
                </li>
              ))}
              {challenge.prizes.length > 3 && (
                <li className="text-xs text-gray-500">
                  +{challenge.prizes.length - 3} more prizes
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex gap-3">
            <button
              onClick={handleJoinChallenge}
              disabled={isJoining || challenge.status !== 'active' || !user}
              className={`flex-1 px-4 py-2 rounded-lg font-medium transition-colors ${
                challenge.status === 'active' && user
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              {isJoining ? 'Joining...' : 
               challenge.status === 'active' ? 'Join Challenge' :
               challenge.status === 'voting' ? 'View & Vote' :
               challenge.status === 'completed' ? 'View Results' :
               'View Challenge'}
            </button>
            
            <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              Details
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
